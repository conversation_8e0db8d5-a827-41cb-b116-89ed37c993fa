<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GrpcChannelFactory</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_class">GrpcChannelFactory</span></div><h1>GrpcChannelFactory</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">71 of 126</td><td class="ctr2">43%</td><td class="bar">1 of 2</td><td class="ctr2">50%</td><td class="ctr1">4</td><td class="ctr2">8</td><td class="ctr1">25</td><td class="ctr2">43</td><td class="ctr1">3</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a3"><a href="GrpcChannelFactory.java.html#L86" class="el_method">createSecureChannel(NodeConfig)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="47" alt="47"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h0">16</td><td class="ctr2" id="i0">16</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="GrpcChannelFactory.java.html#L58" class="el_method">lambda$close$1(ManagedChannel)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="11" alt="11"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h1">5</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="GrpcChannelFactory.java.html#L56" class="el_method">close()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="9" alt="9"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="GrpcChannelFactory.java.html#L43" class="el_method">lambda$createChannel$0(NodeConfig, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="c3">66%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="GrpcChannelFactory.java.html#L67" class="el_method">createInsecureChannel(NodeConfig)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="66" height="10" title="26" alt="26"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i1">11</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="GrpcChannelFactory.java.html#L22" class="el_method">GrpcChannelFactory(RustyClusterClientConfig)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="11" alt="11"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i3">4</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="GrpcChannelFactory.java.html#L41" class="el_method">createChannel(NodeConfig)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="10" alt="10"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>