<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client</a> &gt; <span class="el_source">RustyClusterClient.java</span></div><h1>RustyClusterClient.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.AsyncConnectionManager;
import org.npci.rustyclient.client.connection.ConnectionManager;
import org.npci.rustyclient.client.connection.OperationType;
import org.npci.rustyclient.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Main client for interacting with RustyCluster.
 * This client provides both synchronous and asynchronous operations.
 *
 * Synchronous methods: set(), get(), delete(), hSet(), etc.
 * Asynchronous methods: setAsync(), getAsync(), deleteAsync(), hSetAsync(), etc.
 *
 * Example usage:
 * &lt;pre&gt;
 * RustyClusterClientConfig config = RustyClusterClientConfig.builder()
 *     .addNode(&quot;localhost&quot;, 50051, NodeRole.PRIMARY)
 *     .build();
 *
 * try (RustyClusterClient client = new RustyClusterClient(config)) {
 *     // Synchronous operations
 *     client.set(&quot;key1&quot;, &quot;value1&quot;);
 *     String value = client.get(&quot;key1&quot;);
 *
 *     // Asynchronous operations
 *     CompletableFuture&amp;lt;Boolean&amp;gt; setFuture = client.setAsync(&quot;key2&quot;, &quot;value2&quot;);
 *     CompletableFuture&amp;lt;String&amp;gt; getFuture = client.getAsync(&quot;key2&quot;);
 * }
 * &lt;/pre&gt;
 */
public class RustyClusterClient implements AutoCloseable {
<span class="fc" id="L41">    private static final Logger logger = LoggerFactory.getLogger(RustyClusterClient.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionManager connectionManager;
    private final AsyncConnectionManager asyncConnectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="nc" id="L53">    public RustyClusterClient(RustyClusterClientConfig config) {</span>
<span class="nc" id="L54">        this.config = config;</span>
<span class="nc" id="L55">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="nc" id="L56">        this.connectionManager = new ConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L57">        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L58">        logger.info(&quot;RustyClusterClient initialized with both sync and async capabilities&quot;);</span>
<span class="nc" id="L59">    }</span>

    /**
     * Create a new RustyClusterClient with a custom connection manager (for testing).
     *
     * @param config The client configuration
     * @param connectionManager The connection manager to use
     */
<span class="fc" id="L67">    RustyClusterClient(RustyClusterClientConfig config, ConnectionManager connectionManager) {</span>
<span class="fc" id="L68">        this.config = config;</span>
<span class="fc" id="L69">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="fc" id="L70">        this.connectionManager = connectionManager;</span>
<span class="fc" id="L71">        this.asyncConnectionManager = new AsyncConnectionManager(config, authenticationManager);</span>
<span class="fc" id="L72">        logger.info(&quot;RustyClusterClient initialized with custom connection manager&quot;);</span>
<span class="fc" id="L73">    }</span>

    /**
     * Set a key-value pair.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value, boolean skipReplication) {
<span class="fc" id="L84">        logger.debug(&quot;Setting key: {}&quot;, key);</span>

        try {
<span class="fc" id="L87">            RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()</span>
<span class="fc" id="L88">                    .setKey(key)</span>
<span class="fc" id="L89">                    .setValue(value)</span>
<span class="fc" id="L90">                    .setSkipReplication(skipReplication)</span>
<span class="fc" id="L91">                    .build();</span>

<span class="fc" id="L93">            RustyClusterProto.SetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L94">                    stub.set(request), OperationType.WRITE);</span>

<span class="fc" id="L96">            return response.getSuccess();</span>
<span class="nc" id="L97">        } catch (Exception e) {</span>
<span class="nc" id="L98">            throw e;</span>
        }
    }

    /**
     * Set a key-value pair with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
<span class="fc" id="L110">        return set(key, value, false);</span>
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found
     */
    public String get(String key) {
<span class="fc" id="L120">        logger.debug(&quot;Getting key: {}&quot;, key);</span>
<span class="fc" id="L121">        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="fc" id="L122">                .setKey(key)</span>
<span class="fc" id="L123">                .build();</span>

<span class="fc" id="L125">        RustyClusterProto.GetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L126">                stub.get(request), OperationType.READ);</span>

<span class="fc bfc" id="L128" title="All 2 branches covered.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Delete a key.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean delete(String key, boolean skipReplication) {
<span class="fc" id="L139">        logger.debug(&quot;Deleting key: {}&quot;, key);</span>
<span class="fc" id="L140">        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()</span>
<span class="fc" id="L141">                .setKey(key)</span>
<span class="fc" id="L142">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L143">                .build();</span>

<span class="fc" id="L145">        RustyClusterProto.DeleteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L146">                stub.delete(request), OperationType.WRITE);</span>

<span class="fc" id="L148">        return response.getSuccess();</span>
    }

    /**
     * Delete a key with default replication.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
<span class="fc" id="L158">        return delete(key, false);</span>
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key             The key
     * @param value           The value
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl, boolean skipReplication) {
<span class="fc" id="L171">        logger.debug(&quot;Setting key with expiry: {}, ttl: {}&quot;, key, ttl);</span>
<span class="fc" id="L172">        RustyClusterProto.SetExRequest request = RustyClusterProto.SetExRequest.newBuilder()</span>
<span class="fc" id="L173">                .setKey(key)</span>
<span class="fc" id="L174">                .setValue(value)</span>
<span class="fc" id="L175">                .setTtl(ttl)</span>
<span class="fc" id="L176">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L177">                .build();</span>

<span class="fc" id="L179">        RustyClusterProto.SetExResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L180">                stub.setEx(request), OperationType.WRITE);</span>

<span class="fc" id="L182">        return response.getSuccess();</span>
    }

    /**
     * Set a key-value pair with expiration and default replication.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl) {
<span class="fc" id="L194">        return setEx(key, value, ttl, false);</span>
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key             The key
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl, boolean skipReplication) {
<span class="nc" id="L206">        logger.debug(&quot;Setting expiry on key: {}, ttl: {}&quot;, key, ttl);</span>
<span class="nc" id="L207">        RustyClusterProto.SetExpiryRequest request = RustyClusterProto.SetExpiryRequest.newBuilder()</span>
<span class="nc" id="L208">                .setKey(key)</span>
<span class="nc" id="L209">                .setTtl(ttl)</span>
<span class="nc" id="L210">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L211">                .build();</span>

<span class="nc" id="L213">        RustyClusterProto.SetExpiryResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L214">                stub.setExpiry(request), OperationType.WRITE);</span>

<span class="nc" id="L216">        return response.getSuccess();</span>
    }

    /**
     * Set expiration on an existing key with default replication.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl) {
<span class="nc" id="L227">        return setExpiry(key, ttl, false);</span>
    }

    /**
     * Increment a numeric value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long incrBy(String key, long value, boolean skipReplication) {
<span class="fc" id="L239">        logger.debug(&quot;Incrementing key: {} by {}&quot;, key, value);</span>
<span class="fc" id="L240">        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()</span>
<span class="fc" id="L241">                .setKey(key)</span>
<span class="fc" id="L242">                .setValue(value)</span>
<span class="fc" id="L243">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L244">                .build();</span>

<span class="fc" id="L246">        RustyClusterProto.IncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L247">                stub.incrBy(request), OperationType.WRITE);</span>

<span class="fc" id="L249">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
<span class="fc" id="L260">        return incrBy(key, value, false);</span>
    }

    /**
     * Decrement a numeric value.
     *
     * @param key             The key
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long decrBy(String key, long value, boolean skipReplication) {
<span class="nc" id="L272">        logger.debug(&quot;Decrementing key: {} by {}&quot;, key, value);</span>
<span class="nc" id="L273">        RustyClusterProto.DecrByRequest request = RustyClusterProto.DecrByRequest.newBuilder()</span>
<span class="nc" id="L274">                .setKey(key)</span>
<span class="nc" id="L275">                .setValue(value)</span>
<span class="nc" id="L276">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L277">                .build();</span>

<span class="nc" id="L279">        RustyClusterProto.DecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L280">                stub.decrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L282">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric value with default replication.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
<span class="nc" id="L293">        return decrBy(key, value, false);</span>
    }

    /**
     * Increment a floating-point value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double incrByFloat(String key, double value, boolean skipReplication) {
<span class="nc" id="L305">        logger.debug(&quot;Incrementing key: {} by float {}&quot;, key, value);</span>
<span class="nc" id="L306">        RustyClusterProto.IncrByFloatRequest request = RustyClusterProto.IncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L307">                .setKey(key)</span>
<span class="nc" id="L308">                .setValue(value)</span>
<span class="nc" id="L309">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L310">                .build();</span>

<span class="nc" id="L312">        RustyClusterProto.IncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L313">                stub.incrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L315">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public double incrByFloat(String key, double value) {
<span class="nc" id="L326">        return incrByFloat(key, value, false);</span>
    }

    /**
     * Set a field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L339">        logger.debug(&quot;Setting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L340">        RustyClusterProto.HSetRequest request = RustyClusterProto.HSetRequest.newBuilder()</span>
<span class="nc" id="L341">                .setKey(key)</span>
<span class="nc" id="L342">                .setField(field)</span>
<span class="nc" id="L343">                .setValue(value)</span>
<span class="nc" id="L344">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L345">                .build();</span>

<span class="nc" id="L347">        RustyClusterProto.HSetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L348">                stub.hSet(request), OperationType.WRITE);</span>

<span class="nc" id="L350">        return response.getSuccess();</span>
    }

    /**
     * Set a field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
<span class="nc" id="L362">        return hSet(key, field, value, false);</span>
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found
     */
    public String hGet(String key, String field) {
<span class="nc" id="L373">        logger.debug(&quot;Getting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L374">        RustyClusterProto.HGetRequest request = RustyClusterProto.HGetRequest.newBuilder()</span>
<span class="nc" id="L375">                .setKey(key)</span>
<span class="nc" id="L376">                .setField(field)</span>
<span class="nc" id="L377">                .build();</span>

<span class="nc" id="L379">        RustyClusterProto.HGetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L380">                stub.hGet(request), OperationType.READ);</span>

<span class="nc bnc" id="L382" title="All 2 branches missed.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map&lt;String, String&gt; hGetAll(String key) {
<span class="fc" id="L392">        logger.debug(&quot;Getting all hash fields for key: {}&quot;, key);</span>
<span class="fc" id="L393">        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()</span>
<span class="fc" id="L394">                .setKey(key)</span>
<span class="fc" id="L395">                .build();</span>

<span class="fc" id="L397">        RustyClusterProto.HGetAllResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L398">                stub.hGetAll(request), OperationType.READ);</span>

<span class="fc" id="L400">        return response.getFieldsMap();</span>
    }

    /**
     * Increment a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L413">        logger.debug(&quot;Incrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L414">        RustyClusterProto.HIncrByRequest request = RustyClusterProto.HIncrByRequest.newBuilder()</span>
<span class="nc" id="L415">                .setKey(key)</span>
<span class="nc" id="L416">                .setField(field)</span>
<span class="nc" id="L417">                .setValue(value)</span>
<span class="nc" id="L418">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L419">                .build();</span>

<span class="nc" id="L421">        RustyClusterProto.HIncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L422">                stub.hIncrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L424">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value) {
<span class="nc" id="L436">        return hIncrBy(key, field, value, false);</span>
    }

    /**
     * Decrement a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L449">        logger.debug(&quot;Decrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L450">        RustyClusterProto.HDecrByRequest request = RustyClusterProto.HDecrByRequest.newBuilder()</span>
<span class="nc" id="L451">                .setKey(key)</span>
<span class="nc" id="L452">                .setField(field)</span>
<span class="nc" id="L453">                .setValue(value)</span>
<span class="nc" id="L454">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L455">                .build();</span>

<span class="nc" id="L457">        RustyClusterProto.HDecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L458">                stub.hDecrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L460">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value) {
<span class="nc" id="L472">        return hDecrBy(key, field, value, false);</span>
    }

    /**
     * Increment a floating-point field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value, boolean skipReplication) {
<span class="nc" id="L485">        logger.debug(&quot;Incrementing hash field: {}.{} by float {}&quot;, key, field, value);</span>
<span class="nc" id="L486">        RustyClusterProto.HIncrByFloatRequest request = RustyClusterProto.HIncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L487">                .setKey(key)</span>
<span class="nc" id="L488">                .setField(field)</span>
<span class="nc" id="L489">                .setValue(value)</span>
<span class="nc" id="L490">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L491">                .build();</span>

<span class="nc" id="L493">        RustyClusterProto.HIncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L494">                stub.hIncrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L496">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value) {
<span class="nc" id="L508">        return hIncrByFloat(key, field, value, false);</span>
    }

    /**
     * Execute a batch of operations.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;RustyClusterProto.BatchOperation&gt; operations, boolean skipReplication) {
<span class="fc" id="L519">        logger.debug(&quot;Executing batch write with {} operations&quot;, operations.size());</span>
<span class="fc" id="L520">        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()</span>
<span class="fc" id="L521">                .addAllOperations(operations)</span>
<span class="fc" id="L522">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L523">                .build();</span>

<span class="fc" id="L525">        RustyClusterProto.BatchWriteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L526">                stub.batchWrite(request), OperationType.WRITE);</span>

<span class="fc" id="L528">        return response.getOperationResultsList();</span>
    }

    /**
     * Execute a batch of operations with default replication.
     *
     * @param operations The list of operations to execute
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;RustyClusterProto.BatchOperation&gt; operations) {
<span class="fc" id="L538">        return batchWrite(operations, false);</span>
    }

    /**
     * Authenticate with the RustyCluster server.
     * This method should be called before performing any operations if authentication is configured.
     *
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate() {
<span class="nc" id="L548">        logger.debug(&quot;Attempting to authenticate with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L550" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L551">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L552">            return true;</span>
        }

        try {
            // Get a stub from the connection manager and authenticate
<span class="nc" id="L557">            return connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L558">                authenticationManager.authenticate(stub), OperationType.AUTH);</span>
<span class="nc" id="L559">        } catch (Exception e) {</span>
<span class="nc" id="L560">            logger.error(&quot;Authentication failed&quot;, e);</span>
<span class="nc" id="L561">            return false;</span>
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="nc" id="L571">        return authenticationManager.isAuthenticated();</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="nc" id="L580">        return authenticationManager.getSessionToken();</span>
    }

    // ==================== NEW METHODS ====================

    /**
     * Set multiple fields in a hash.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map&lt;String, String&gt; fields, boolean skipReplication) {
<span class="nc" id="L594">        logger.debug(&quot;Setting multiple hash fields for key: {}, fields count: {}&quot;, key, fields.size());</span>

        // For now, we'll implement this using individual hSet calls until gRPC classes are regenerated
        // This is a temporary implementation
<span class="nc" id="L598">        boolean allSuccess = true;</span>
<span class="nc bnc" id="L599" title="All 2 branches missed.">        for (Map.Entry&lt;String, String&gt; entry : fields.entrySet()) {</span>
<span class="nc" id="L600">            boolean result = hSet(key, entry.getKey(), entry.getValue(), skipReplication);</span>
<span class="nc bnc" id="L601" title="All 2 branches missed.">            if (!result) {</span>
<span class="nc" id="L602">                allSuccess = false;</span>
            }
        }
<span class="nc" id="L605">        return allSuccess;</span>
    }

    /**
     * Set multiple fields in a hash with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return True if successful, false otherwise
     */
    public boolean hMSet(String key, Map&lt;String, String&gt; fields) {
<span class="nc" id="L616">        return hMSet(key, fields, false);</span>
    }

    /**
     * Check if a hash field exists.
     *
     * @param key   The hash key
     * @param field The field name
     * @return True if field exists, false otherwise
     */
    public boolean hExists(String key, String field) {
<span class="nc" id="L627">        logger.debug(&quot;Checking if hash field exists: {}.{}&quot;, key, field);</span>

        // Temporary implementation using hGet until gRPC classes are regenerated
<span class="nc" id="L630">        String value = hGet(key, field);</span>
<span class="nc bnc" id="L631" title="All 2 branches missed.">        return value != null;</span>
    }

    /**
     * Set a key-value pair only if the key does not exist.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value, boolean skipReplication) {
<span class="nc" id="L643">        logger.debug(&quot;Setting key if not exists: {}&quot;, key);</span>

        // Temporary implementation using exists check + set until gRPC classes are regenerated
<span class="nc bnc" id="L646" title="All 2 branches missed.">        if (exists(key)) {</span>
<span class="nc" id="L647">            return false; // Key already exists</span>
        }
<span class="nc" id="L649">        return set(key, value, skipReplication);</span>
    }

    /**
     * Set a key-value pair only if the key does not exist with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if key was set, false if key already existed
     */
    public boolean setNX(String key, String value) {
<span class="nc" id="L660">        return setNX(key, value, false);</span>
    }

    /**
     * Check if a key exists.
     *
     * @param key The key
     * @return True if key exists, false otherwise
     */
    public boolean exists(String key) {
<span class="nc" id="L670">        logger.debug(&quot;Checking if key exists: {}&quot;, key);</span>

        // Temporary implementation using get until gRPC classes are regenerated
<span class="nc" id="L673">        String value = get(key);</span>
<span class="nc bnc" id="L674" title="All 2 branches missed.">        return value != null;</span>
    }

    /**
     * Load a Lua script and return its SHA hash.
     *
     * @param script The Lua script to load
     * @return The SHA hash of the loaded script, or null if failed
     */
    public String loadScript(String script) {
<span class="nc" id="L684">        logger.debug(&quot;Loading script, length: {}&quot;, script.length());</span>

        // Temporary implementation - return a mock SHA until gRPC classes are regenerated
        // In real implementation, this would call the LoadScript RPC
<span class="nc" id="L688">        logger.warn(&quot;loadScript is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L689">        return null;</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List&lt;String&gt; keys, List&lt;String&gt; args, boolean skipReplication) {
<span class="nc" id="L702">        logger.debug(&quot;Executing script with SHA: {}, keys: {}, args: {}&quot;, sha, keys.size(), args.size());</span>

        // Temporary implementation until gRPC classes are regenerated
<span class="nc" id="L705">        logger.warn(&quot;evalSha is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L706">        return null;</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return The script execution result, or null if failed
     */
    public String evalSha(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
<span class="nc" id="L718">        return evalSha(sha, keys, args, false);</span>
    }

    /**
     * Perform a health check on the cluster.
     *
     * @return True if healthy, false otherwise
     */
    public boolean healthCheck() {
<span class="nc" id="L727">        logger.debug(&quot;Performing health check&quot;);</span>

        // Use ping for now until HealthCheck RPC is available
        try {
<span class="nc" id="L731">            return ping();</span>
<span class="nc" id="L732">        } catch (Exception e) {</span>
<span class="nc" id="L733">            logger.warn(&quot;Health check failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L734">            return false;</span>
        }
    }

    /**
     * Ping the cluster to check connectivity.
     *
     * @return True if ping successful, false otherwise
     */
    public boolean ping() {
<span class="nc" id="L744">        logger.debug(&quot;Pinging cluster&quot;);</span>

        try {
<span class="nc" id="L747">            RustyClusterProto.PingRequest request = RustyClusterProto.PingRequest.newBuilder().build();</span>
<span class="nc" id="L748">            RustyClusterProto.PingResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L749">                    stub.ping(request), OperationType.READ);</span>
<span class="nc" id="L750">            return response.getSuccess();</span>
<span class="nc" id="L751">        } catch (Exception e) {</span>
<span class="nc" id="L752">            logger.warn(&quot;Ping failed: {}&quot;, e.getMessage());</span>
<span class="nc" id="L753">            return false;</span>
        }
    }

    // ==================== ASYNCHRONOUS METHODS ====================

    /**
     * Set a key-value pair asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value, boolean skipReplication) {
<span class="nc" id="L768">        logger.debug(&quot;Setting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L769">        RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()</span>
<span class="nc" id="L770">                .setKey(key)</span>
<span class="nc" id="L771">                .setValue(value)</span>
<span class="nc" id="L772">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L773">                .build();</span>

<span class="nc" id="L775">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L776">                stub.set(request))</span>
<span class="nc" id="L777">                .thenApply(RustyClusterProto.SetResponse::getSuccess);</span>
    }

    /**
     * Set a key-value pair asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; setAsync(String key, String value) {
<span class="nc" id="L788">        return setAsync(key, value, false);</span>
    }

    /**
     * Get a value by key asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with the value, or null if not found
     */
    public CompletableFuture&lt;String&gt; getAsync(String key) {
<span class="nc" id="L798">        logger.debug(&quot;Getting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L799">        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="nc" id="L800">                .setKey(key)</span>
<span class="nc" id="L801">                .build();</span>

<span class="nc" id="L803">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L804">                stub.get(request))</span>
<span class="nc bnc" id="L805" title="All 2 branches missed.">                .thenApply(response -&gt; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Delete a key asynchronously.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key, boolean skipReplication) {
<span class="nc" id="L816">        logger.debug(&quot;Deleting key asynchronously: {}&quot;, key);</span>
<span class="nc" id="L817">        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()</span>
<span class="nc" id="L818">                .setKey(key)</span>
<span class="nc" id="L819">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L820">                .build();</span>

<span class="nc" id="L822">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L823">                stub.delete(request))</span>
<span class="nc" id="L824">                .thenApply(RustyClusterProto.DeleteResponse::getSuccess);</span>
    }

    /**
     * Delete a key asynchronously with default replication.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; deleteAsync(String key) {
<span class="nc" id="L834">        return deleteAsync(key, false);</span>
    }

    /**
     * Execute a batch of operations asynchronously.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;RustyClusterProto.BatchOperation&gt; operations, boolean skipReplication) {
<span class="nc" id="L845">        logger.debug(&quot;Executing batch write asynchronously with {} operations&quot;, operations.size());</span>
<span class="nc" id="L846">        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()</span>
<span class="nc" id="L847">                .addAllOperations(operations)</span>
<span class="nc" id="L848">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L849">                .build();</span>

<span class="nc" id="L851">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L852">                stub.batchWrite(request))</span>
<span class="nc" id="L853">                .thenApply(RustyClusterProto.BatchWriteResponse::getOperationResultsList);</span>
    }

    /**
     * Execute a batch of operations asynchronously with default replication.
     *
     * @param operations The list of operations to execute
     * @return CompletableFuture that completes with a list of results, one for each operation
     */
    public CompletableFuture&lt;List&lt;Boolean&gt;&gt; batchWriteAsync(List&lt;RustyClusterProto.BatchOperation&gt; operations) {
<span class="nc" id="L863">        return batchWriteAsync(operations, false);</span>
    }

    /**
     * Increment a numeric value asynchronously.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value, boolean skipReplication) {
<span class="nc" id="L875">        logger.debug(&quot;Incrementing key asynchronously: {} by {}&quot;, key, value);</span>
<span class="nc" id="L876">        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()</span>
<span class="nc" id="L877">                .setKey(key)</span>
<span class="nc" id="L878">                .setValue(value)</span>
<span class="nc" id="L879">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L880">                .build();</span>

<span class="nc" id="L882">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L883">                stub.incrBy(request))</span>
<span class="nc" id="L884">                .thenApply(RustyClusterProto.IncrByResponse::getNewValue);</span>
    }

    /**
     * Increment a numeric value asynchronously with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return CompletableFuture that completes with the new value
     */
    public CompletableFuture&lt;Long&gt; incrByAsync(String key, long value) {
<span class="nc" id="L895">        return incrByAsync(key, value, false);</span>
    }

    /**
     * Set a hash field asynchronously.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L908">        logger.debug(&quot;Setting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="nc" id="L909">        RustyClusterProto.HSetRequest request = RustyClusterProto.HSetRequest.newBuilder()</span>
<span class="nc" id="L910">                .setKey(key)</span>
<span class="nc" id="L911">                .setField(field)</span>
<span class="nc" id="L912">                .setValue(value)</span>
<span class="nc" id="L913">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L914">                .build();</span>

<span class="nc" id="L916">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L917">                stub.hSet(request))</span>
<span class="nc" id="L918">                .thenApply(RustyClusterProto.HSetResponse::getSuccess);</span>
    }

    /**
     * Set a hash field asynchronously with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hSetAsync(String key, String field, String value) {
<span class="nc" id="L930">        return hSetAsync(key, field, value, false);</span>
    }

    /**
     * Get a hash field asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with the field value, or null if not found
     */
    public CompletableFuture&lt;String&gt; hGetAsync(String key, String field) {
<span class="nc" id="L941">        logger.debug(&quot;Getting hash field asynchronously: {}.{}&quot;, key, field);</span>
<span class="nc" id="L942">        RustyClusterProto.HGetRequest request = RustyClusterProto.HGetRequest.newBuilder()</span>
<span class="nc" id="L943">                .setKey(key)</span>
<span class="nc" id="L944">                .setField(field)</span>
<span class="nc" id="L945">                .build();</span>

<span class="nc" id="L947">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L948">                stub.hGet(request))</span>
<span class="nc bnc" id="L949" title="All 2 branches missed.">                .thenApply(response -&gt; response.getFound() ? response.getValue() : null);</span>
    }

    /**
     * Get all fields from a hash asynchronously.
     *
     * @param key The hash key
     * @return CompletableFuture that completes with a map of field names to values
     */
    public CompletableFuture&lt;Map&lt;String, String&gt;&gt; hGetAllAsync(String key) {
<span class="nc" id="L959">        logger.debug(&quot;Getting all hash fields asynchronously for key: {}&quot;, key);</span>
<span class="nc" id="L960">        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()</span>
<span class="nc" id="L961">                .setKey(key)</span>
<span class="nc" id="L962">                .build();</span>

<span class="nc" id="L964">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L965">                stub.hGetAll(request))</span>
<span class="nc" id="L966">                .thenApply(RustyClusterProto.HGetAllResponse::getFieldsMap);</span>
    }

    // ==================== NEW ASYNC METHODS ====================

    /**
     * Set multiple fields in a hash asynchronously.
     *
     * @param key             The hash key
     * @param fields          Map of field-value pairs
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields, boolean skipReplication) {
<span class="nc" id="L980">        logger.debug(&quot;Setting multiple hash fields asynchronously for key: {}, fields count: {}&quot;, key, fields.size());</span>

        // For now, we'll implement this using individual hSet calls until gRPC classes are regenerated
        // This is a temporary implementation
<span class="nc" id="L984">        List&lt;CompletableFuture&lt;Boolean&gt;&gt; futures = fields.entrySet().stream()</span>
<span class="nc" id="L985">                .map(entry -&gt; hSetAsync(key, entry.getKey(), entry.getValue(), skipReplication))</span>
<span class="nc" id="L986">                .toList();</span>

<span class="nc" id="L988">        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))</span>
<span class="nc" id="L989">                .thenApply(v -&gt; futures.stream().allMatch(CompletableFuture::join));</span>
    }

    /**
     * Set multiple fields in a hash asynchronously with default replication.
     *
     * @param key    The hash key
     * @param fields Map of field-value pairs
     * @return CompletableFuture that completes with true if successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hMSetAsync(String key, Map&lt;String, String&gt; fields) {
<span class="nc" id="L1000">        return hMSetAsync(key, fields, false);</span>
    }

    /**
     * Check if a hash field exists asynchronously.
     *
     * @param key   The hash key
     * @param field The field name
     * @return CompletableFuture that completes with true if field exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; hExistsAsync(String key, String field) {
<span class="nc" id="L1011">        logger.debug(&quot;Checking if hash field exists asynchronously: {}.{}&quot;, key, field);</span>

        // Temporary implementation using hGet until gRPC classes are regenerated
<span class="nc bnc" id="L1014" title="All 2 branches missed.">        return hGetAsync(key, field).thenApply(value -&gt; value != null);</span>
    }

    /**
     * Check if a key exists asynchronously.
     *
     * @param key The key
     * @return CompletableFuture that completes with true if key exists, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; existsAsync(String key) {
<span class="nc" id="L1024">        logger.debug(&quot;Checking if key exists asynchronously: {}&quot;, key);</span>

        // Temporary implementation using get until gRPC classes are regenerated
<span class="nc bnc" id="L1027" title="All 2 branches missed.">        return getAsync(key).thenApply(value -&gt; value != null);</span>
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value, boolean skipReplication) {
<span class="nc" id="L1039">        logger.debug(&quot;Setting key if not exists asynchronously: {}&quot;, key);</span>

        // Temporary implementation using exists check + set until gRPC classes are regenerated
<span class="nc" id="L1042">        return existsAsync(key).thenCompose(exists -&gt; {</span>
<span class="nc bnc" id="L1043" title="All 2 branches missed.">            if (exists) {</span>
<span class="nc" id="L1044">                return CompletableFuture.completedFuture(false); // Key already exists</span>
            }
<span class="nc" id="L1046">            return setAsync(key, value, skipReplication);</span>
        });
    }

    /**
     * Set a key-value pair only if the key does not exist asynchronously with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return CompletableFuture that completes with true if key was set, false if key already existed
     */
    public CompletableFuture&lt;Boolean&gt; setNXAsync(String key, String value) {
<span class="nc" id="L1058">        return setNXAsync(key, value, false);</span>
    }

    /**
     * Load a Lua script and return its SHA hash asynchronously.
     *
     * @param script The Lua script to load
     * @return CompletableFuture that completes with the SHA hash of the loaded script, or null if failed
     */
    public CompletableFuture&lt;String&gt; loadScriptAsync(String script) {
<span class="nc" id="L1068">        logger.debug(&quot;Loading script asynchronously, length: {}&quot;, script.length());</span>

        // Temporary implementation - return a mock SHA until gRPC classes are regenerated
        // In real implementation, this would call the LoadScript RPC
<span class="nc" id="L1072">        logger.warn(&quot;loadScriptAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L1073">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously.
     *
     * @param sha             The SHA hash of the script
     * @param keys            List of keys to pass to the script
     * @param args            List of arguments to pass to the script
     * @param skipReplication Whether to skip replication
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args, boolean skipReplication) {
<span class="nc" id="L1086">        logger.debug(&quot;Executing script asynchronously with SHA: {}, keys: {}, args: {}&quot;, sha, keys.size(), args.size());</span>

        // Temporary implementation until gRPC classes are regenerated
<span class="nc" id="L1089">        logger.warn(&quot;evalShaAsync is not fully implemented yet - requires gRPC class regeneration&quot;);</span>
<span class="nc" id="L1090">        return CompletableFuture.completedFuture(null);</span>
    }

    /**
     * Execute a previously loaded Lua script by its SHA hash asynchronously with default replication.
     *
     * @param sha  The SHA hash of the script
     * @param keys List of keys to pass to the script
     * @param args List of arguments to pass to the script
     * @return CompletableFuture that completes with the script execution result, or null if failed
     */
    public CompletableFuture&lt;String&gt; evalShaAsync(String sha, List&lt;String&gt; keys, List&lt;String&gt; args) {
<span class="nc" id="L1102">        return evalShaAsync(sha, keys, args, false);</span>
    }

    /**
     * Perform a health check on the cluster asynchronously.
     *
     * @return CompletableFuture that completes with true if healthy, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; healthCheckAsync() {
<span class="nc" id="L1111">        logger.debug(&quot;Performing health check asynchronously&quot;);</span>

        // Use ping for now until HealthCheck RPC is available
<span class="nc" id="L1114">        return pingAsync().exceptionally(throwable -&gt; {</span>
<span class="nc" id="L1115">            logger.warn(&quot;Health check failed: {}&quot;, throwable.getMessage());</span>
<span class="nc" id="L1116">            return false;</span>
        });
    }

    /**
     * Ping the cluster to check connectivity asynchronously.
     *
     * @return CompletableFuture that completes with true if ping successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; pingAsync() {
<span class="nc" id="L1126">        logger.debug(&quot;Pinging cluster asynchronously&quot;);</span>

<span class="nc" id="L1128">        RustyClusterProto.PingRequest request = RustyClusterProto.PingRequest.newBuilder().build();</span>
<span class="nc" id="L1129">        return asyncConnectionManager.executeWithFailoverAsync(stub -&gt;</span>
<span class="nc" id="L1130">                stub.ping(request))</span>
<span class="nc" id="L1131">                .thenApply(RustyClusterProto.PingResponse::getSuccess)</span>
<span class="nc" id="L1132">                .exceptionally(throwable -&gt; {</span>
<span class="nc" id="L1133">                    logger.warn(&quot;Ping failed: {}&quot;, throwable.getMessage());</span>
<span class="nc" id="L1134">                    return false;</span>
                });
    }

    /**
     * Authenticate with the RustyCluster server asynchronously.
     *
     * @return CompletableFuture that completes with true if authentication was successful, false otherwise
     */
    public CompletableFuture&lt;Boolean&gt; authenticateAsync() {
<span class="nc" id="L1144">        logger.debug(&quot;Attempting to authenticate asynchronously with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L1146" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L1147">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L1148">            return CompletableFuture.completedFuture(true);</span>
        }

        // For async authentication, we need to use a different approach
        // since authenticate method expects a blocking stub
<span class="nc" id="L1153">        return CompletableFuture.supplyAsync(() -&gt; {</span>
            try {
                // This is a simplified approach - in a real implementation,
                // you'd want to create an async version of authenticate
<span class="nc" id="L1157">                return authenticationManager.isAuthenticated();</span>
<span class="nc" id="L1158">            } catch (Exception e) {</span>
<span class="nc" id="L1159">                logger.error(&quot;Authentication check failed&quot;, e);</span>
<span class="nc" id="L1160">                return false;</span>
            }
        });
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L1170">        authenticationManager.clearAuthentication();</span>
<span class="fc" id="L1171">        connectionManager.close();</span>
<span class="fc" id="L1172">        asyncConnectionManager.close();</span>
<span class="fc" id="L1173">        logger.info(&quot;RustyClusterClient closed&quot;);</span>
<span class="fc" id="L1174">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>