<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RustyClusterClient.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client</a> &gt; <span class="el_source">RustyClusterClient.java</span></div><h1>RustyClusterClient.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.ConnectionManager;
import org.npci.rustyclient.client.connection.OperationType;
import org.npci.rustyclient.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Client for interacting with RustyCluster.
 */
public class RustyClusterClient implements AutoCloseable {
<span class="fc" id="L18">    private static final Logger logger = LoggerFactory.getLogger(RustyClusterClient.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionManager connectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterClient with the provided configuration.
     *
     * @param config The client configuration
     */
<span class="nc" id="L29">    public RustyClusterClient(RustyClusterClientConfig config) {</span>
<span class="nc" id="L30">        this.config = config;</span>
<span class="nc" id="L31">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="nc" id="L32">        this.connectionManager = new ConnectionManager(config, authenticationManager);</span>
<span class="nc" id="L33">        logger.info(&quot;RustyClusterClient initialized&quot;);</span>
<span class="nc" id="L34">    }</span>

    /**
     * Create a new RustyClusterClient with a custom connection manager (for testing).
     *
     * @param config The client configuration
     * @param connectionManager The connection manager to use
     */
<span class="fc" id="L42">    RustyClusterClient(RustyClusterClientConfig config, ConnectionManager connectionManager) {</span>
<span class="fc" id="L43">        this.config = config;</span>
<span class="fc" id="L44">        this.authenticationManager = new AuthenticationManager(config);</span>
<span class="fc" id="L45">        this.connectionManager = connectionManager;</span>
<span class="fc" id="L46">        logger.info(&quot;RustyClusterClient initialized&quot;);</span>
<span class="fc" id="L47">    }</span>

    /**
     * Set a key-value pair.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value, boolean skipReplication) {
<span class="fc" id="L58">        logger.debug(&quot;Setting key: {}&quot;, key);</span>

        try {
<span class="fc" id="L61">            RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()</span>
<span class="fc" id="L62">                    .setKey(key)</span>
<span class="fc" id="L63">                    .setValue(value)</span>
<span class="fc" id="L64">                    .setSkipReplication(skipReplication)</span>
<span class="fc" id="L65">                    .build();</span>

<span class="fc" id="L67">            RustyClusterProto.SetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L68">                    stub.set(request), OperationType.WRITE);</span>

<span class="fc" id="L70">            return response.getSuccess();</span>
<span class="nc" id="L71">        } catch (Exception e) {</span>
<span class="nc" id="L72">            throw e;</span>
        }
    }

    /**
     * Set a key-value pair with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
<span class="fc" id="L84">        return set(key, value, false);</span>
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found
     */
    public String get(String key) {
<span class="fc" id="L94">        logger.debug(&quot;Getting key: {}&quot;, key);</span>
<span class="fc" id="L95">        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="fc" id="L96">                .setKey(key)</span>
<span class="fc" id="L97">                .build();</span>

<span class="fc" id="L99">        RustyClusterProto.GetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L100">                stub.get(request), OperationType.READ);</span>

<span class="fc bfc" id="L102" title="All 2 branches covered.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Delete a key.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean delete(String key, boolean skipReplication) {
<span class="fc" id="L113">        logger.debug(&quot;Deleting key: {}&quot;, key);</span>
<span class="fc" id="L114">        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()</span>
<span class="fc" id="L115">                .setKey(key)</span>
<span class="fc" id="L116">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L117">                .build();</span>

<span class="fc" id="L119">        RustyClusterProto.DeleteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L120">                stub.delete(request), OperationType.WRITE);</span>

<span class="fc" id="L122">        return response.getSuccess();</span>
    }

    /**
     * Delete a key with default replication.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
<span class="fc" id="L132">        return delete(key, false);</span>
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key             The key
     * @param value           The value
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl, boolean skipReplication) {
<span class="fc" id="L145">        logger.debug(&quot;Setting key with expiry: {}, ttl: {}&quot;, key, ttl);</span>
<span class="fc" id="L146">        RustyClusterProto.SetExRequest request = RustyClusterProto.SetExRequest.newBuilder()</span>
<span class="fc" id="L147">                .setKey(key)</span>
<span class="fc" id="L148">                .setValue(value)</span>
<span class="fc" id="L149">                .setTtl(ttl)</span>
<span class="fc" id="L150">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L151">                .build();</span>

<span class="fc" id="L153">        RustyClusterProto.SetExResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L154">                stub.setEx(request), OperationType.WRITE);</span>

<span class="fc" id="L156">        return response.getSuccess();</span>
    }

    /**
     * Set a key-value pair with expiration and default replication.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl) {
<span class="fc" id="L168">        return setEx(key, value, ttl, false);</span>
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key             The key
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl, boolean skipReplication) {
<span class="nc" id="L180">        logger.debug(&quot;Setting expiry on key: {}, ttl: {}&quot;, key, ttl);</span>
<span class="nc" id="L181">        RustyClusterProto.SetExpiryRequest request = RustyClusterProto.SetExpiryRequest.newBuilder()</span>
<span class="nc" id="L182">                .setKey(key)</span>
<span class="nc" id="L183">                .setTtl(ttl)</span>
<span class="nc" id="L184">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L185">                .build();</span>

<span class="nc" id="L187">        RustyClusterProto.SetExpiryResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L188">                stub.setExpiry(request), OperationType.WRITE);</span>

<span class="nc" id="L190">        return response.getSuccess();</span>
    }

    /**
     * Set expiration on an existing key with default replication.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl) {
<span class="nc" id="L201">        return setExpiry(key, ttl, false);</span>
    }

    /**
     * Increment a numeric value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long incrBy(String key, long value, boolean skipReplication) {
<span class="fc" id="L213">        logger.debug(&quot;Incrementing key: {} by {}&quot;, key, value);</span>
<span class="fc" id="L214">        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()</span>
<span class="fc" id="L215">                .setKey(key)</span>
<span class="fc" id="L216">                .setValue(value)</span>
<span class="fc" id="L217">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L218">                .build();</span>

<span class="fc" id="L220">        RustyClusterProto.IncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L221">                stub.incrBy(request), OperationType.WRITE);</span>

<span class="fc" id="L223">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
<span class="fc" id="L234">        return incrBy(key, value, false);</span>
    }

    /**
     * Decrement a numeric value.
     *
     * @param key             The key
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long decrBy(String key, long value, boolean skipReplication) {
<span class="nc" id="L246">        logger.debug(&quot;Decrementing key: {} by {}&quot;, key, value);</span>
<span class="nc" id="L247">        RustyClusterProto.DecrByRequest request = RustyClusterProto.DecrByRequest.newBuilder()</span>
<span class="nc" id="L248">                .setKey(key)</span>
<span class="nc" id="L249">                .setValue(value)</span>
<span class="nc" id="L250">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L251">                .build();</span>

<span class="nc" id="L253">        RustyClusterProto.DecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L254">                stub.decrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L256">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric value with default replication.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
<span class="nc" id="L267">        return decrBy(key, value, false);</span>
    }

    /**
     * Increment a floating-point value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double incrByFloat(String key, double value, boolean skipReplication) {
<span class="nc" id="L279">        logger.debug(&quot;Incrementing key: {} by float {}&quot;, key, value);</span>
<span class="nc" id="L280">        RustyClusterProto.IncrByFloatRequest request = RustyClusterProto.IncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L281">                .setKey(key)</span>
<span class="nc" id="L282">                .setValue(value)</span>
<span class="nc" id="L283">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L284">                .build();</span>

<span class="nc" id="L286">        RustyClusterProto.IncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L287">                stub.incrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L289">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public double incrByFloat(String key, double value) {
<span class="nc" id="L300">        return incrByFloat(key, value, false);</span>
    }

    /**
     * Set a field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value, boolean skipReplication) {
<span class="nc" id="L313">        logger.debug(&quot;Setting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L314">        RustyClusterProto.HSetRequest request = RustyClusterProto.HSetRequest.newBuilder()</span>
<span class="nc" id="L315">                .setKey(key)</span>
<span class="nc" id="L316">                .setField(field)</span>
<span class="nc" id="L317">                .setValue(value)</span>
<span class="nc" id="L318">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L319">                .build();</span>

<span class="nc" id="L321">        RustyClusterProto.HSetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L322">                stub.hSet(request), OperationType.WRITE);</span>

<span class="nc" id="L324">        return response.getSuccess();</span>
    }

    /**
     * Set a field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
<span class="nc" id="L336">        return hSet(key, field, value, false);</span>
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found
     */
    public String hGet(String key, String field) {
<span class="nc" id="L347">        logger.debug(&quot;Getting hash field: {}.{}&quot;, key, field);</span>
<span class="nc" id="L348">        RustyClusterProto.HGetRequest request = RustyClusterProto.HGetRequest.newBuilder()</span>
<span class="nc" id="L349">                .setKey(key)</span>
<span class="nc" id="L350">                .setField(field)</span>
<span class="nc" id="L351">                .build();</span>

<span class="nc" id="L353">        RustyClusterProto.HGetResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L354">                stub.hGet(request), OperationType.READ);</span>

<span class="nc bnc" id="L356" title="All 2 branches missed.">        return response.getFound() ? response.getValue() : null;</span>
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map&lt;String, String&gt; hGetAll(String key) {
<span class="fc" id="L366">        logger.debug(&quot;Getting all hash fields for key: {}&quot;, key);</span>
<span class="fc" id="L367">        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()</span>
<span class="fc" id="L368">                .setKey(key)</span>
<span class="fc" id="L369">                .build();</span>

<span class="fc" id="L371">        RustyClusterProto.HGetAllResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L372">                stub.hGetAll(request), OperationType.READ);</span>

<span class="fc" id="L374">        return response.getFieldsMap();</span>
    }

    /**
     * Increment a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L387">        logger.debug(&quot;Incrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L388">        RustyClusterProto.HIncrByRequest request = RustyClusterProto.HIncrByRequest.newBuilder()</span>
<span class="nc" id="L389">                .setKey(key)</span>
<span class="nc" id="L390">                .setField(field)</span>
<span class="nc" id="L391">                .setValue(value)</span>
<span class="nc" id="L392">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L393">                .build();</span>

<span class="nc" id="L395">        RustyClusterProto.HIncrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L396">                stub.hIncrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L398">        return response.getNewValue();</span>
    }

    /**
     * Increment a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value) {
<span class="nc" id="L410">        return hIncrBy(key, field, value, false);</span>
    }

    /**
     * Decrement a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value, boolean skipReplication) {
<span class="nc" id="L423">        logger.debug(&quot;Decrementing hash field: {}.{} by {}&quot;, key, field, value);</span>
<span class="nc" id="L424">        RustyClusterProto.HDecrByRequest request = RustyClusterProto.HDecrByRequest.newBuilder()</span>
<span class="nc" id="L425">                .setKey(key)</span>
<span class="nc" id="L426">                .setField(field)</span>
<span class="nc" id="L427">                .setValue(value)</span>
<span class="nc" id="L428">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L429">                .build();</span>

<span class="nc" id="L431">        RustyClusterProto.HDecrByResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L432">                stub.hDecrBy(request), OperationType.WRITE);</span>

<span class="nc" id="L434">        return response.getNewValue();</span>
    }

    /**
     * Decrement a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value) {
<span class="nc" id="L446">        return hDecrBy(key, field, value, false);</span>
    }

    /**
     * Increment a floating-point field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value, boolean skipReplication) {
<span class="nc" id="L459">        logger.debug(&quot;Incrementing hash field: {}.{} by float {}&quot;, key, field, value);</span>
<span class="nc" id="L460">        RustyClusterProto.HIncrByFloatRequest request = RustyClusterProto.HIncrByFloatRequest.newBuilder()</span>
<span class="nc" id="L461">                .setKey(key)</span>
<span class="nc" id="L462">                .setField(field)</span>
<span class="nc" id="L463">                .setValue(value)</span>
<span class="nc" id="L464">                .setSkipReplication(skipReplication)</span>
<span class="nc" id="L465">                .build();</span>

<span class="nc" id="L467">        RustyClusterProto.HIncrByFloatResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L468">                stub.hIncrByFloat(request), OperationType.WRITE);</span>

<span class="nc" id="L470">        return response.getNewValue();</span>
    }

    /**
     * Increment a floating-point field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value) {
<span class="nc" id="L482">        return hIncrByFloat(key, field, value, false);</span>
    }

    /**
     * Execute a batch of operations.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;RustyClusterProto.BatchOperation&gt; operations, boolean skipReplication) {
<span class="fc" id="L493">        logger.debug(&quot;Executing batch write with {} operations&quot;, operations.size());</span>
<span class="fc" id="L494">        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()</span>
<span class="fc" id="L495">                .addAllOperations(operations)</span>
<span class="fc" id="L496">                .setSkipReplication(skipReplication)</span>
<span class="fc" id="L497">                .build();</span>

<span class="fc" id="L499">        RustyClusterProto.BatchWriteResponse response = connectionManager.executeWithFailover(stub -&gt;</span>
<span class="fc" id="L500">                stub.batchWrite(request), OperationType.WRITE);</span>

<span class="fc" id="L502">        return response.getOperationResultsList();</span>
    }

    /**
     * Execute a batch of operations with default replication.
     *
     * @param operations The list of operations to execute
     * @return A list of results, one for each operation
     */
    public List&lt;Boolean&gt; batchWrite(List&lt;RustyClusterProto.BatchOperation&gt; operations) {
<span class="fc" id="L512">        return batchWrite(operations, false);</span>
    }

    /**
     * Authenticate with the RustyCluster server.
     * This method should be called before performing any operations if authentication is configured.
     *
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate() {
<span class="nc" id="L522">        logger.debug(&quot;Attempting to authenticate with RustyCluster server&quot;);</span>

<span class="nc bnc" id="L524" title="All 2 branches missed.">        if (!config.hasAuthentication()) {</span>
<span class="nc" id="L525">            logger.debug(&quot;No authentication credentials configured&quot;);</span>
<span class="nc" id="L526">            return true;</span>
        }

        try {
            // Get a stub from the connection manager and authenticate
<span class="nc" id="L531">            return connectionManager.executeWithFailover(stub -&gt;</span>
<span class="nc" id="L532">                authenticationManager.authenticate(stub), OperationType.AUTH);</span>
<span class="nc" id="L533">        } catch (Exception e) {</span>
<span class="nc" id="L534">            logger.error(&quot;Authentication failed&quot;, e);</span>
<span class="nc" id="L535">            return false;</span>
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
<span class="nc" id="L545">        return authenticationManager.isAuthenticated();</span>
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
<span class="nc" id="L554">        return authenticationManager.getSessionToken();</span>
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L562">        authenticationManager.clearAuthentication();</span>
<span class="fc" id="L563">        connectionManager.close();</span>
<span class="fc" id="L564">        logger.info(&quot;RustyClusterClient closed&quot;);</span>
<span class="fc" id="L565">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>