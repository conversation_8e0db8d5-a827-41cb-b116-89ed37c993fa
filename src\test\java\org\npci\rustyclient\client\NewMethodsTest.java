package org.npci.rustyclient.client;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.npci.rustyclient.client.config.NodeRole;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for the new methods added to RustyCluster client.
 * These tests verify the implementation of HMSET, HEXISTS, EXISTS, SETNX, 
 * LoadScript, EvalSha, and HealthCheck methods.
 */
public class NewMethodsTest {

    private RustyClusterClient client;
    private RustyClusterAsyncClient asyncClient;
    private RustyClusterClientConfig config;

    @BeforeEach
    void setUp() {
        config = RustyClusterClientConfig.builder()
                .addNode("localhost", 50051, NodeRole.PRIMARY)
                .addNode("localhost", 50052, NodeRole.SECONDARY)
                .connectionTimeout(5, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .writeTimeout(3, TimeUnit.SECONDS)
                .maxRetries(2)
                .retryDelay(100, TimeUnit.MILLISECONDS)
                .build();

        client = new RustyClusterClient(config);
        asyncClient = new RustyClusterAsyncClient(config);
    }

    @Test
    @DisplayName("Test HMSET functionality")
    void testHMSet() {
        Map<String, String> fields = new HashMap<>();
        fields.put("field1", "value1");
        fields.put("field2", "value2");
        fields.put("field3", "value3");

        // Test synchronous HMSET
        boolean result = client.hMSet("test:hash", fields);
        assertTrue(result, "HMSET should succeed");

        // Verify fields were set by checking individual fields
        assertEquals("value1", client.hGet("test:hash", "field1"));
        assertEquals("value2", client.hGet("test:hash", "field2"));
        assertEquals("value3", client.hGet("test:hash", "field3"));

        // Test with skipReplication
        boolean resultSkipReplication = client.hMSet("test:hash2", fields, true);
        assertTrue(resultSkipReplication, "HMSET with skipReplication should succeed");
    }

    @Test
    @DisplayName("Test HMSET async functionality")
    void testHMSetAsync() throws Exception {
        Map<String, String> fields = new HashMap<>();
        fields.put("async_field1", "async_value1");
        fields.put("async_field2", "async_value2");

        // Test asynchronous HMSET
        CompletableFuture<Boolean> future = asyncClient.hMSetAsync("test:async:hash", fields);
        Boolean result = future.get(5, TimeUnit.SECONDS);
        assertTrue(result, "Async HMSET should succeed");

        // Verify fields were set
        CompletableFuture<String> getValue1 = asyncClient.hGetAsync("test:async:hash", "async_field1");
        CompletableFuture<String> getValue2 = asyncClient.hGetAsync("test:async:hash", "async_field2");
        
        assertEquals("async_value1", getValue1.get(5, TimeUnit.SECONDS));
        assertEquals("async_value2", getValue2.get(5, TimeUnit.SECONDS));
    }

    @Test
    @DisplayName("Test HEXISTS functionality")
    void testHExists() {
        // Set up test data
        client.hSet("test:exists:hash", "existing_field", "some_value");

        // Test existing field
        assertTrue(client.hExists("test:exists:hash", "existing_field"), 
                "HEXISTS should return true for existing field");

        // Test non-existing field
        assertFalse(client.hExists("test:exists:hash", "non_existing_field"), 
                "HEXISTS should return false for non-existing field");

        // Test non-existing hash
        assertFalse(client.hExists("test:non:existing:hash", "any_field"), 
                "HEXISTS should return false for non-existing hash");
    }

    @Test
    @DisplayName("Test HEXISTS async functionality")
    void testHExistsAsync() throws Exception {
        // Set up test data
        asyncClient.hSetAsync("test:async:exists:hash", "existing_field", "some_value")
                .get(5, TimeUnit.SECONDS);

        // Test existing field
        CompletableFuture<Boolean> existsResult = asyncClient.hExistsAsync("test:async:exists:hash", "existing_field");
        assertTrue(existsResult.get(5, TimeUnit.SECONDS), 
                "Async HEXISTS should return true for existing field");

        // Test non-existing field
        CompletableFuture<Boolean> notExistsResult = asyncClient.hExistsAsync("test:async:exists:hash", "non_existing_field");
        assertFalse(notExistsResult.get(5, TimeUnit.SECONDS), 
                "Async HEXISTS should return false for non-existing field");
    }

    @Test
    @DisplayName("Test EXISTS functionality")
    void testExists() {
        // Set up test data
        client.set("test:existing:key", "some_value");

        // Test existing key
        assertTrue(client.exists("test:existing:key"), 
                "EXISTS should return true for existing key");

        // Test non-existing key
        assertFalse(client.exists("test:non:existing:key"), 
                "EXISTS should return false for non-existing key");
    }

    @Test
    @DisplayName("Test EXISTS async functionality")
    void testExistsAsync() throws Exception {
        // Set up test data
        asyncClient.setAsync("test:async:existing:key", "some_value")
                .get(5, TimeUnit.SECONDS);

        // Test existing key
        CompletableFuture<Boolean> existsResult = asyncClient.existsAsync("test:async:existing:key");
        assertTrue(existsResult.get(5, TimeUnit.SECONDS), 
                "Async EXISTS should return true for existing key");

        // Test non-existing key
        CompletableFuture<Boolean> notExistsResult = asyncClient.existsAsync("test:async:non:existing:key");
        assertFalse(notExistsResult.get(5, TimeUnit.SECONDS), 
                "Async EXISTS should return false for non-existing key");
    }

    @Test
    @DisplayName("Test SETNX functionality")
    void testSetNX() {
        String key = "test:setnx:key";
        String value1 = "first_value";
        String value2 = "second_value";

        // First SETNX should succeed
        assertTrue(client.setNX(key, value1), 
                "SETNX should succeed when key doesn't exist");

        // Verify the value was set
        assertEquals(value1, client.get(key), 
                "Key should have the first value");

        // Second SETNX should fail
        assertFalse(client.setNX(key, value2), 
                "SETNX should fail when key already exists");

        // Verify the value wasn't changed
        assertEquals(value1, client.get(key), 
                "Key should still have the first value");

        // Test with skipReplication
        String key2 = "test:setnx:key2";
        assertTrue(client.setNX(key2, value1, true), 
                "SETNX with skipReplication should succeed when key doesn't exist");
    }

    @Test
    @DisplayName("Test SETNX async functionality")
    void testSetNXAsync() throws Exception {
        String key = "test:async:setnx:key";
        String value1 = "first_async_value";
        String value2 = "second_async_value";

        // First SETNX should succeed
        CompletableFuture<Boolean> firstResult = asyncClient.setNXAsync(key, value1);
        assertTrue(firstResult.get(5, TimeUnit.SECONDS), 
                "Async SETNX should succeed when key doesn't exist");

        // Verify the value was set
        CompletableFuture<String> getValue = asyncClient.getAsync(key);
        assertEquals(value1, getValue.get(5, TimeUnit.SECONDS), 
                "Key should have the first value");

        // Second SETNX should fail
        CompletableFuture<Boolean> secondResult = asyncClient.setNXAsync(key, value2);
        assertFalse(secondResult.get(5, TimeUnit.SECONDS), 
                "Async SETNX should fail when key already exists");
    }

    @Test
    @DisplayName("Test LoadScript functionality")
    void testLoadScript() {
        String script = "return 'Hello from Lua!'";

        // Test load script (currently returns null as it's not fully implemented)
        String sha = client.loadScript(script);
        // For now, we expect null since it's not fully implemented
        assertNull(sha, "LoadScript should return null until fully implemented");
    }

    @Test
    @DisplayName("Test LoadScript async functionality")
    void testLoadScriptAsync() throws Exception {
        String script = "return 'Hello from async Lua!'";

        // Test async load script
        CompletableFuture<String> shaFuture = asyncClient.loadScriptAsync(script);
        String sha = shaFuture.get(5, TimeUnit.SECONDS);
        // For now, we expect null since it's not fully implemented
        assertNull(sha, "Async LoadScript should return null until fully implemented");
    }

    @Test
    @DisplayName("Test EvalSha functionality")
    void testEvalSha() {
        String sha = "dummy_sha_hash";
        List<String> keys = List.of("key1", "key2");
        List<String> args = List.of("arg1", "arg2");

        // Test eval sha (currently returns null as it's not fully implemented)
        String result = client.evalSha(sha, keys, args);
        assertNull(result, "EvalSha should return null until fully implemented");

        // Test with skipReplication
        String resultSkipReplication = client.evalSha(sha, keys, args, true);
        assertNull(resultSkipReplication, "EvalSha with skipReplication should return null until fully implemented");
    }

    @Test
    @DisplayName("Test EvalSha async functionality")
    void testEvalShaAsync() throws Exception {
        String sha = "dummy_async_sha_hash";
        List<String> keys = List.of("async_key1", "async_key2");
        List<String> args = List.of("async_arg1", "async_arg2");

        // Test async eval sha
        CompletableFuture<String> resultFuture = asyncClient.evalShaAsync(sha, keys, args);
        String result = resultFuture.get(5, TimeUnit.SECONDS);
        assertNull(result, "Async EvalSha should return null until fully implemented");
    }

    @Test
    @DisplayName("Test HealthCheck functionality")
    void testHealthCheck() {
        // Test health check (uses ping internally)
        boolean isHealthy = client.healthCheck();
        // The result depends on whether a server is running, so we just verify it doesn't throw
        assertNotNull(isHealthy, "HealthCheck should return a boolean value");
    }

    @Test
    @DisplayName("Test HealthCheck async functionality")
    void testHealthCheckAsync() throws Exception {
        // Test async health check
        CompletableFuture<Boolean> healthFuture = asyncClient.healthCheckAsync();
        Boolean isHealthy = healthFuture.get(5, TimeUnit.SECONDS);
        assertNotNull(isHealthy, "Async HealthCheck should return a boolean value");
    }

    @Test
    @DisplayName("Test Ping functionality")
    void testPing() {
        // Test ping
        boolean pingResult = client.ping();
        // The result depends on whether a server is running, so we just verify it doesn't throw
        assertNotNull(pingResult, "Ping should return a boolean value");
    }

    @Test
    @DisplayName("Test Ping async functionality")
    void testPingAsync() throws Exception {
        // Test async ping
        CompletableFuture<Boolean> pingFuture = asyncClient.pingAsync();
        Boolean pingResult = pingFuture.get(5, TimeUnit.SECONDS);
        assertNotNull(pingResult, "Async Ping should return a boolean value");
    }

    @Test
    @DisplayName("Test BatchOperationBuilder new methods")
    void testBatchOperationBuilderNewMethods() {
        BatchOperationBuilder builder = new BatchOperationBuilder();

        // Test adding HMSET operation
        Map<String, String> fields = Map.of("field1", "value1", "field2", "value2");
        builder.addHMSet("test:batch:hash", fields);

        // Test adding SETNX operation
        builder.addSetNX("test:batch:setnx", "value");

        // Test adding LoadScript operation
        builder.addLoadScript("return 'test'");

        // Test adding EvalSha operation
        builder.addEvalSha("dummy_sha", List.of("key1"), List.of("arg1"));

        // Build operations
        var operations = builder.build();
        assertNotNull(operations, "Builder should return operations list");
        // The exact count depends on how HMSET is implemented (individual HSET operations)
        assertTrue(operations.size() >= 2, "Should have at least 2 operations from HMSET");
    }
}
