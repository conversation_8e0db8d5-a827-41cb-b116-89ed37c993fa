<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConnectionManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">ConnectionManager.java</span></div><h1>ConnectionManager.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.exception.NoAvailableNodesException;
import org.npci.rustyclient.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages connections to RustyCluster nodes, handling prioritization and failover.
 */
public class ConnectionManager implements AutoCloseable {
<span class="fc" id="L20">    private static final Logger logger = LoggerFactory.getLogger(ConnectionManager.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionPool connectionPool;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final FailbackManager failbackManager;

    /**
     * Create a new ConnectionManager.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
    public ConnectionManager(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {
<span class="nc" id="L35">        this(config, new ConnectionPool(config, authenticationManager));</span>
<span class="nc" id="L36">    }</span>

    /**
     * Create a new ConnectionManager with a custom connection pool (for testing).
     *
     * @param config The client configuration
     * @param connectionPool The connection pool to use
     */
<span class="fc" id="L44">    ConnectionManager(RustyClusterClientConfig config, ConnectionPool connectionPool) {</span>
<span class="fc" id="L45">        this.config = config;</span>
<span class="fc" id="L46">        this.connectionPool = connectionPool;</span>

        // Sort nodes by priority (PRIMARY, SECONDARY, TERTIARY)
<span class="fc" id="L49">        this.sortedNodes = config.getNodes().stream()</span>
<span class="fc" id="L50">                .sorted(Comparator.comparingInt(node -&gt; node.role().getPriority()))</span>
<span class="fc" id="L51">                .toList();</span>

        // Set the initial node to the highest priority node
<span class="fc" id="L54">        this.currentNode = new AtomicReference&lt;&gt;(sortedNodes.get(0));</span>

        // Initialize failback manager
<span class="fc" id="L57">        this.failbackManager = new FailbackManager(config, connectionPool, sortedNodes, currentNode);</span>
<span class="fc" id="L58">        this.failbackManager.start();</span>

<span class="fc" id="L60">        logger.info(&quot;ConnectionManager initialized with {} nodes&quot;, sortedNodes.size());</span>
<span class="fc" id="L61">    }</span>

    /**
     * Execute an operation with automatic failover.
     *
     * @param operation The operation to execute
     * @param &lt;T&gt;       The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(ClientOperation&lt;T&gt; operation) throws NoAvailableNodesException {
<span class="fc" id="L72">        return executeWithFailover(operation, OperationType.READ);</span>
    }

    /**
     * Execute an operation with automatic failover and specific timeout based on operation type.
     *
     * @param operation     The operation to execute
     * @param operationType The type of operation (READ, WRITE, AUTH) to determine appropriate timeout
     * @param &lt;T&gt;           The return type of the operation
     * @return The result of the operation
     * @throws NoAvailableNodesException If no nodes are available
     */
    public &lt;T&gt; T executeWithFailover(ClientOperation&lt;T&gt; operation, OperationType operationType) throws NoAvailableNodesException {
<span class="fc" id="L85">        int retries = 0;</span>
<span class="fc" id="L86">        Exception lastException = null;</span>

        // Determine timeout based on operation type
<span class="pc bpc" id="L89" title="1 of 4 branches missed.">        long timeoutMs = switch (operationType) {</span>
<span class="fc" id="L90">            case READ -&gt; config.getReadTimeoutMs();</span>
<span class="fc" id="L91">            case WRITE -&gt; config.getWriteTimeoutMs();</span>
<span class="fc" id="L92">            case AUTH -&gt; config.getConnectionTimeoutMs();</span>
        };

<span class="fc bfc" id="L95" title="All 2 branches covered.">        while (retries &lt;= config.getMaxRetries()) {</span>
<span class="fc" id="L96">            NodeConfig node = currentNode.get();</span>
<span class="fc" id="L97">            KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;</span>

            try {
                // Ensure authentication before executing non-auth operations
<span class="fc bfc" id="L101" title="All 4 branches covered.">                if (operationType != OperationType.AUTH &amp;&amp; config.hasAuthentication() &amp;&amp;</span>
<span class="fc bfc" id="L102" title="All 2 branches covered.">                    !connectionPool.getAuthenticationManager().isAuthenticated()) {</span>
<span class="fc" id="L103">                    logger.debug(&quot;Authentication required before operation, attempting to authenticate&quot;);</span>
                    try {
<span class="fc" id="L105">                        boolean authResult = connectionPool.getAuthenticationManager().authenticate(</span>
<span class="fc" id="L106">                            connectionPool.borrowStub(node).withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS));</span>
<span class="pc bpc" id="L107" title="1 of 2 branches missed.">                        if (!authResult) {</span>
<span class="nc" id="L108">                            throw new RuntimeException(&quot;Authentication failed before operation&quot;);</span>
                        }
<span class="nc" id="L110">                    } catch (Exception authException) {</span>
<span class="nc" id="L111">                        logger.warn(&quot;Pre-operation authentication failed on node {}: {}&quot;, node, authException.getMessage());</span>
<span class="nc" id="L112">                        throw authException;</span>
<span class="fc" id="L113">                    }</span>
                }

<span class="fc" id="L116">                stub = connectionPool.borrowStub(node);</span>
                // Apply deadline per operation to avoid expired deadline issues
<span class="fc" id="L118">                KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline =</span>
<span class="fc" id="L119">                    stub.withDeadlineAfter(timeoutMs, TimeUnit.MILLISECONDS);</span>
<span class="fc" id="L120">                return operation.execute(stubWithDeadline);</span>
<span class="fc" id="L121">            } catch (Exception e) {</span>
<span class="fc" id="L122">                lastException = e;</span>
<span class="fc" id="L123">                logger.warn(&quot;Operation failed on node {}: {}&quot;, node, e.getMessage());</span>

                // Check if this is an authentication error and try to re-authenticate
<span class="pc bpc" id="L126" title="2 of 6 branches missed.">                if (isAuthenticationError(e) &amp;&amp; operationType != OperationType.AUTH &amp;&amp; config.hasAuthentication()) {</span>
<span class="fc" id="L127">                    logger.info(&quot;Authentication error detected, clearing auth state and will retry&quot;);</span>
<span class="fc" id="L128">                    connectionPool.getAuthenticationManager().clearAuthentication();</span>
                }

                // Try to find the next available node
<span class="fc" id="L132">                var nextNode = findNextAvailableNode(node);</span>
<span class="fc bfc" id="L133" title="All 2 branches covered.">                if (nextNode != null) {</span>
<span class="fc" id="L134">                    currentNode.set(nextNode);</span>
<span class="fc" id="L135">                    logger.info(&quot;Switched to node: {}&quot;, nextNode);</span>

                    // Clear authentication state when switching nodes
                    // This will force re-authentication on the next operation
<span class="fc bfc" id="L139" title="All 2 branches covered.">                    if (config.hasAuthentication()) {</span>
<span class="fc" id="L140">                        connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="fc" id="L141">                        logger.debug(&quot;Cleared authentication state for node switch to: {}&quot;, nextNode);</span>
                    }
                } else {
<span class="fc" id="L144">                    logger.warn(&quot;No available nodes found after failure&quot;);</span>
                }

<span class="fc" id="L147">                retries++;</span>

<span class="fc bfc" id="L149" title="All 2 branches covered.">                if (retries &lt;= config.getMaxRetries()) {</span>
                    try {
<span class="fc" id="L151">                        Thread.sleep(config.getRetryDelayMs());</span>
<span class="nc" id="L152">                    } catch (InterruptedException ie) {</span>
<span class="nc" id="L153">                        Thread.currentThread().interrupt();</span>
<span class="nc" id="L154">                        throw new RuntimeException(&quot;Interrupted during retry delay&quot;, ie);</span>
<span class="fc" id="L155">                    }</span>
                }
            } finally {
<span class="fc bfc" id="L158" title="All 2 branches covered.">                if (stub != null) {</span>
<span class="fc" id="L159">                    connectionPool.returnStub(node, stub);</span>
                }
            }
<span class="fc" id="L162">        }</span>

<span class="fc" id="L164">        throw new NoAvailableNodesException(&quot;Operation failed after &quot; + retries + &quot; retries&quot;, lastException);</span>
    }

    /**
     * Find the next available node after a failure.
     *
     * @param failedNode The node that failed
     * @return The next available node, or null if none are available
     */
    private NodeConfig findNextAvailableNode(NodeConfig failedNode) {
        // First try to find a node with the same priority
<span class="fc" id="L175">        var samePriorityNode = sortedNodes.stream()</span>
<span class="pc bpc" id="L176" title="1 of 4 branches missed.">                .filter(node -&gt; node.role() == failedNode.role() &amp;&amp; !node.equals(failedNode))</span>
<span class="fc" id="L177">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L178">                .findFirst();</span>

<span class="pc bpc" id="L180" title="1 of 2 branches missed.">        if (samePriorityNode.isPresent()) {</span>
<span class="nc" id="L181">            return samePriorityNode.get();</span>
        }

        // Then try to find a node with lower priority
<span class="fc" id="L185">        var lowerPriorityNode = sortedNodes.stream()</span>
<span class="fc bfc" id="L186" title="All 2 branches covered.">                .filter(node -&gt; node.role().getPriority() &gt; failedNode.role().getPriority())</span>
<span class="fc" id="L187">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L188">                .findFirst();</span>

<span class="fc bfc" id="L190" title="All 2 branches covered.">        if (lowerPriorityNode.isPresent()) {</span>
<span class="fc" id="L191">            return lowerPriorityNode.get();</span>
        }

        // Finally, try any node except the failed one
<span class="fc" id="L195">        return sortedNodes.stream()</span>
<span class="fc bfc" id="L196" title="All 2 branches covered.">                .filter(node -&gt; !node.equals(failedNode))</span>
<span class="fc" id="L197">                .filter(this::isNodeAvailable)</span>
<span class="fc" id="L198">                .findFirst()</span>
<span class="fc" id="L199">                .orElse(null);</span>
    }

    /**
     * Check if a node is available.
     *
     * @param node The node to check
     * @return True if the node is available, false otherwise
     */
    private boolean isNodeAvailable(NodeConfig node) {
<span class="fc" id="L209">        KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;</span>
        try {
<span class="fc" id="L211">            stub = connectionPool.borrowStub(node);</span>
            // In a real implementation, you might want to perform a health check
<span class="fc" id="L213">            return true;</span>
<span class="fc" id="L214">        } catch (Exception e) {</span>
<span class="fc" id="L215">            logger.warn(&quot;Node {} is not available: {}&quot;, node, e.getMessage());</span>
<span class="fc" id="L216">            return false;</span>
        } finally {
<span class="fc bfc" id="L218" title="All 2 branches covered.">            if (stub != null) {</span>
<span class="fc" id="L219">                connectionPool.returnStub(node, stub);</span>
            }
        }
    }



    /**
     * Check if an exception indicates an authentication error.
     *
     * @param exception The exception to check
     * @return True if this is an authentication error, false otherwise
     */
    private boolean isAuthenticationError(Exception exception) {
<span class="pc bpc" id="L233" title="1 of 2 branches missed.">        if (exception == null) {</span>
<span class="nc" id="L234">            return false;</span>
        }

<span class="fc" id="L237">        String message = exception.getMessage();</span>
<span class="pc bpc" id="L238" title="1 of 2 branches missed.">        if (message == null) {</span>
<span class="nc" id="L239">            return false;</span>
        }

        // Check for common authentication error patterns
<span class="fc" id="L243">        String lowerMessage = message.toLowerCase();</span>
<span class="fc bfc" id="L244" title="All 2 branches covered.">        return lowerMessage.contains(&quot;unauthenticated&quot;) ||</span>
<span class="pc bpc" id="L245" title="1 of 2 branches missed.">               lowerMessage.contains(&quot;authentication failed&quot;) ||</span>
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">               lowerMessage.contains(&quot;invalid token&quot;) ||</span>
<span class="pc bpc" id="L247" title="1 of 2 branches missed.">               lowerMessage.contains(&quot;unauthorized&quot;) ||</span>
<span class="pc bpc" id="L248" title="2 of 4 branches missed.">               lowerMessage.contains(&quot;permission denied&quot;) ||</span>
               (exception instanceof io.grpc.StatusRuntimeException &amp;&amp;
<span class="pc bnc" id="L250" title="All 2 branches missed.">                ((io.grpc.StatusRuntimeException) exception).getStatus().getCode() == io.grpc.Status.Code.UNAUTHENTICATED);</span>
    }

    /**
     * Close the connection manager and release all resources.
     */
    @Override
    public void close() {
<span class="fc" id="L258">        failbackManager.close();</span>
<span class="fc" id="L259">        connectionPool.close();</span>
<span class="fc" id="L260">        logger.info(&quot;ConnectionManager closed&quot;);</span>
<span class="fc" id="L261">    }</span>

    /**
     * Functional interface for client operations.
     *
     * @param &lt;T&gt; The return type of the operation
     */
    @FunctionalInterface
    public interface ClientOperation&lt;T&gt; {
        /**
         * Execute an operation using the provided client stub.
         *
         * @param stub The client stub
         * @return The result of the operation
         * @throws Exception If the operation fails
         */
        T execute(KeyValueServiceGrpc.KeyValueServiceBlockingStub stub) throws Exception;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>