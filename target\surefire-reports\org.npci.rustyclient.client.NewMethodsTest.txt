-------------------------------------------------------------------------------
Test set: org.npci.rustyclient.client.NewMethodsTest
-------------------------------------------------------------------------------
Tests run: 17, Failures: 0, Errors: 8, Skipped: 0, Time elapsed: 5.517 s <<< FAILURE! -- in org.npci.rustyclient.client.NewMethodsTest
org.npci.rustyclient.client.NewMethodsTest.testHMSet -- Time elapsed: 1.626 s <<< ERROR!
org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.hSet(RustyClusterClient.java:321)
	at org.npci.rustyclient.client.RustyClusterClient.hMSet(RustyClusterClient.java:574)
	at org.npci.rustyclient.client.RustyClusterClient.hMSet(RustyClusterClient.java:590)
	at org.npci.rustyclient.client.NewMethodsTest.testHMSet(NewMethodsTest.java:53)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.hSet(KeyValueServiceGrpc.java:1017)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$hSet$8(RustyClusterClient.java:322)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 7 more

org.npci.rustyclient.client.NewMethodsTest.testSetNX -- Time elapsed: 0.265 s <<< ERROR!
org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.get(RustyClusterClient.java:99)
	at org.npci.rustyclient.client.RustyClusterClient.exists(RustyClusterClient.java:647)
	at org.npci.rustyclient.client.RustyClusterClient.setNX(RustyClusterClient.java:620)
	at org.npci.rustyclient.client.RustyClusterClient.setNX(RustyClusterClient.java:634)
	at org.npci.rustyclient.client.NewMethodsTest.testSetNX(NewMethodsTest.java:164)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.get(KeyValueServiceGrpc.java:962)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$get$1(RustyClusterClient.java:100)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 8 more

org.npci.rustyclient.client.NewMethodsTest.testSetNXAsync -- Time elapsed: 0.387 s <<< ERROR!
java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testSetNXAsync(NewMethodsTest.java:194)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)

org.npci.rustyclient.client.NewMethodsTest.testHExists -- Time elapsed: 0.259 s <<< ERROR!
org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.hSet(RustyClusterClient.java:321)
	at org.npci.rustyclient.client.RustyClusterClient.hSet(RustyClusterClient.java:336)
	at org.npci.rustyclient.client.NewMethodsTest.testHExists(NewMethodsTest.java:90)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.hSet(KeyValueServiceGrpc.java:1017)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$hSet$8(RustyClusterClient.java:322)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 6 more

org.npci.rustyclient.client.NewMethodsTest.testHMSetAsync -- Time elapsed: 0.395 s <<< ERROR!
java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testHMSetAsync(NewMethodsTest.java:75)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)

org.npci.rustyclient.client.NewMethodsTest.testHExistsAsync -- Time elapsed: 0.358 s <<< ERROR!
java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testHExistsAsync(NewMethodsTest.java:110)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)

org.npci.rustyclient.client.NewMethodsTest.testExists -- Time elapsed: 0.286 s <<< ERROR!
org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.set(RustyClusterClient.java:67)
	at org.npci.rustyclient.client.RustyClusterClient.set(RustyClusterClient.java:84)
	at org.npci.rustyclient.client.NewMethodsTest.testExists(NewMethodsTest.java:127)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.set(KeyValueServiceGrpc.java:955)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$set$0(RustyClusterClient.java:68)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 6 more

org.npci.rustyclient.client.NewMethodsTest.testExistsAsync -- Time elapsed: 0.395 s <<< ERROR!
java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testExistsAsync(NewMethodsTest.java:143)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)

