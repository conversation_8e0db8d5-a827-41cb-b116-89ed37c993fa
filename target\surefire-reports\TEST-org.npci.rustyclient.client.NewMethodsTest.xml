<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.npci.rustyclient.client.NewMethodsTest" time="5.517" tests="17" errors="8" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\test-classes;c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\classes;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.59.0\grpc-netty-shaded-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\32.0.1-android\guava-32.0.1-android.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.20.0\error_prone_annotations-2.20.0.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.59.0\grpc-core-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.59.0\grpc-context-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.59.0\grpc-protobuf-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.59.0\grpc-api-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.22.0\proto-google-common-protos-2.22.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.59.0\grpc-protobuf-lite-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.59.0\grpc-stub-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.24.0\protobuf-java-3.24.0.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.4.0\mockito-core-5.4.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.5\byte-buddy-1.14.5.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.5\byte-buddy-agent-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.4.0\mockito-junit-jupiter-5.4.0.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-testing\1.59.0\grpc-testing-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.59.0\grpc-inprocess-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.59.0\grpc-util-1.59.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Calcutta"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="IN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire17455749418871181303\surefirebooter-20250604144735082_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire17455749418871181303 2025-06-04T14-47-34_694-jvmRun1 surefire-20250604144735082_1tmp surefire_0-20250604144735082_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\test-classes;c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client\target\classes;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.59.0\grpc-netty-shaded-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\32.0.1-android\guava-32.0.1-android.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.20.0\error_prone_annotations-2.20.0.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.59.0\grpc-core-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.59.0\grpc-context-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.59.0\grpc-protobuf-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.59.0\grpc-api-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.22.0\proto-google-common-protos-2.22.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.59.0\grpc-protobuf-lite-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.59.0\grpc-stub-1.59.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.24.0\protobuf-java-3.24.0.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.4.0\mockito-core-5.4.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.5\byte-buddy-1.14.5.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.5\byte-buddy-agent-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.4.0\mockito-junit-jupiter-5.4.0.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-testing\1.59.0\grpc-testing-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.59.0\grpc-inprocess-1.59.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.59.0\grpc-util-1.59.0.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire17455749418871181303\surefirebooter-20250604144735082_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.12+8-LTS-286"/>
    <property name="user.name" value="localadmin"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.12"/>
    <property name="user.dir" value="c:\Users\<USER>\Documents\augment-projects\rustyclient\rustycluster-java-client"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;%JAVA_HOME%\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\Desktop\protoc-30.2-win64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\Documents\apache-maven-3.9.9\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.12+8-LTS-286"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testEvalSha" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.132">
    <system-out><![CDATA[14:47:46.820 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:46.821 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:46.821 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:46.821 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:46.822 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:46.825 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:46.826 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:46.827 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:46.827 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:46.827 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:46.828 [main] WARN  o.n.r.client.RustyClusterClient - evalSha is not fully implemented yet - requires gRPC class regeneration
14:47:46.835 [main] WARN  o.n.r.client.RustyClusterClient - evalSha is not fully implemented yet - requires gRPC class regeneration
]]></system-out>
  </testcase>
  <testcase name="testHMSet" classname="org.npci.rustyclient.client.NewMethodsTest" time="1.626">
    <error message="Operation failed after 3 retries" type="org.npci.rustyclient.client.exception.NoAvailableNodesException"><![CDATA[org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.hSet(RustyClusterClient.java:321)
	at org.npci.rustyclient.client.RustyClusterClient.hMSet(RustyClusterClient.java:574)
	at org.npci.rustyclient.client.RustyClusterClient.hMSet(RustyClusterClient.java:590)
	at org.npci.rustyclient.client.NewMethodsTest.testHMSet(NewMethodsTest.java:53)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.hSet(KeyValueServiceGrpc.java:1017)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$hSet$8(RustyClusterClient.java:322)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 7 more
]]></error>
    <system-out><![CDATA[14:47:46.839 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:46.841 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:46.842 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:46.842 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:46.842 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:46.844 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:46.847 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:46.848 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:46.848 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:46.848 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:48.112 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.114 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.234 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.235 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.429 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.429 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testSetNX" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.265">
    <error message="Operation failed after 3 retries" type="org.npci.rustyclient.client.exception.NoAvailableNodesException"><![CDATA[org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.get(RustyClusterClient.java:99)
	at org.npci.rustyclient.client.RustyClusterClient.exists(RustyClusterClient.java:647)
	at org.npci.rustyclient.client.RustyClusterClient.setNX(RustyClusterClient.java:620)
	at org.npci.rustyclient.client.RustyClusterClient.setNX(RustyClusterClient.java:634)
	at org.npci.rustyclient.client.NewMethodsTest.testSetNX(NewMethodsTest.java:164)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.get(KeyValueServiceGrpc.java:962)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$get$1(RustyClusterClient.java:100)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 8 more
]]></error>
    <system-out><![CDATA[14:47:48.477 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.480 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.480 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:48.480 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:48.480 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:48.483 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.488 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.489 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:48.489 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:48.489 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:48.503 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.505 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.616 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.617 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.737 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.737 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testPing" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.242">
    <system-out><![CDATA[14:47:48.742 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.744 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.744 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:48.744 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:48.744 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:48.747 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.752 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.753 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:48.753 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:48.753 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:48.766 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.770 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.876 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.877 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.981 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:48.981 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.981 [main] WARN  o.n.r.client.RustyClusterClient - Ping failed: Operation failed after 3 retries
]]></system-out>
  </testcase>
  <testcase name="testLoadScript" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.012">
    <system-out><![CDATA[14:47:48.987 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.988 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.988 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:48.988 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:48.988 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:48.990 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:48.993 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:48.994 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:48.995 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:48.995 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:48.995 [main] WARN  o.n.r.client.RustyClusterClient - loadScript is not fully implemented yet - requires gRPC class regeneration
]]></system-out>
  </testcase>
  <testcase name="testEvalShaAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.015">
    <system-out><![CDATA[14:47:48.999 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.003 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.004 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:49.004 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:49.004 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:49.006 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.008 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.010 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:49.010 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:49.010 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:49.010 [main] WARN  o.n.r.client.RustyClusterAsyncClient - evalShaAsync is not fully implemented yet - requires gRPC class regeneration
]]></system-out>
  </testcase>
  <testcase name="testSetNXAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.387">
    <error message="org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries" type="java.util.concurrent.ExecutionException"><![CDATA[java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testSetNXAsync(NewMethodsTest.java:194)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
]]></error>
    <system-out><![CDATA[14:47:49.015 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.020 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.020 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:49.020 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:49.020 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:49.023 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.034 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.035 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:49.036 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:49.036 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:49.057 [ForkJoinPool.commonPool-worker-1] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.060 [ForkJoinPool.commonPool-worker-1] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.175 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.175 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.293 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.293 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testHExists" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.259">
    <error message="Operation failed after 3 retries" type="org.npci.rustyclient.client.exception.NoAvailableNodesException"><![CDATA[org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.hSet(RustyClusterClient.java:321)
	at org.npci.rustyclient.client.RustyClusterClient.hSet(RustyClusterClient.java:336)
	at org.npci.rustyclient.client.NewMethodsTest.testHExists(NewMethodsTest.java:90)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.hSet(KeyValueServiceGrpc.java:1017)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$hSet$8(RustyClusterClient.java:322)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 6 more
]]></error>
    <system-out><![CDATA[14:47:49.402 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.404 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.405 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:49.405 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:49.405 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:49.407 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.408 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.408 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:49.408 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:49.408 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:49.415 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:49.416 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.539 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: UNAUTHENTICATED: Missing authorization header
14:47:49.540 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.654 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:49.655 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testHMSetAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.395">
    <error message="org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries" type="java.util.concurrent.ExecutionException"><![CDATA[java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testHMSetAsync(NewMethodsTest.java:75)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
]]></error>
    <system-out><![CDATA[14:47:49.681 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.685 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.686 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:49.686 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:49.686 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:49.688 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.692 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.693 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:49.693 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:49.693 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:49.703 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.703 [ForkJoinPool.commonPool-worker-4] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.704 [ForkJoinPool.commonPool-worker-4] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.704 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.826 [ForkJoinPool.commonPool-worker-4] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.826 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.827 [ForkJoinPool.commonPool-worker-4] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.827 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:49.945 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.945 [ForkJoinPool.commonPool-worker-4] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:49.945 [ForkJoinPool.commonPool-worker-4] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:49.945 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testPingAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.383">
    <system-out><![CDATA[14:47:50.062 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.070 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.072 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:50.072 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:50.072 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:50.078 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.083 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.085 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:50.085 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:50.085 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:50.096 [ForkJoinPool.commonPool-worker-5] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:50.097 [ForkJoinPool.commonPool-worker-5] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.213 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:50.213 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.321 [ForkJoinPool.commonPool-worker-5] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:50.321 [ForkJoinPool.commonPool-worker-5] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.438 [ForkJoinPool.commonPool-worker-5] WARN  o.n.r.client.RustyClusterAsyncClient - Ping failed: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
]]></system-out>
  </testcase>
  <testcase name="testHExistsAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.358">
    <error message="org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries" type="java.util.concurrent.ExecutionException"><![CDATA[java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testHExistsAsync(NewMethodsTest.java:110)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
]]></error>
    <system-out><![CDATA[14:47:50.441 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.443 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.443 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:50.444 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:50.444 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:50.446 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.447 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.448 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:50.448 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:50.448 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:50.456 [ForkJoinPool.commonPool-worker-5] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:50.457 [ForkJoinPool.commonPool-worker-5] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.575 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:50.576 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.693 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:50.693 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testBatchOperationBuilderNewMethods" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.032">
    <system-out><![CDATA[14:47:50.805 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.809 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.810 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:50.810 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:50.810 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:50.821 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.825 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.826 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:50.826 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:50.827 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:50.828 [main] WARN  o.n.r.client.BatchOperationBuilder - SETNX batch operation not fully implemented yet - using SET as placeholder
14:47:50.828 [main] WARN  o.n.r.client.BatchOperationBuilder - LOAD_SCRIPT batch operation not fully implemented yet - requires gRPC class regeneration
14:47:50.828 [main] WARN  o.n.r.client.BatchOperationBuilder - EVALSHA batch operation not fully implemented yet - requires gRPC class regeneration
]]></system-out>
  </testcase>
  <testcase name="testExists" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.286">
    <error message="Operation failed after 3 retries" type="org.npci.rustyclient.client.exception.NoAvailableNodesException"><![CDATA[org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:164)
	at org.npci.rustyclient.client.RustyClusterClient.set(RustyClusterClient.java:67)
	at org.npci.rustyclient.client.RustyClusterClient.set(RustyClusterClient.java:84)
	at org.npci.rustyclient.client.NewMethodsTest.testExists(NewMethodsTest.java:127)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
	at io.grpc.stub.ClientCalls.toStatusRuntimeException(ClientCalls.java:268)
	at io.grpc.stub.ClientCalls.getUnchecked(ClientCalls.java:249)
	at io.grpc.stub.ClientCalls.blockingUnaryCall(ClientCalls.java:167)
	at org.npci.rustyclient.grpc.KeyValueServiceGrpc$KeyValueServiceBlockingStub.set(KeyValueServiceGrpc.java:955)
	at org.npci.rustyclient.client.RustyClusterClient.lambda$set$0(RustyClusterClient.java:68)
	at org.npci.rustyclient.client.connection.ConnectionManager.executeWithFailover(ConnectionManager.java:120)
	... 6 more
]]></error>
    <system-out><![CDATA[14:47:50.846 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.856 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.856 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:50.856 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:50.856 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:50.866 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:50.874 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:50.875 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:50.875 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:50.875 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:50.890 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:50.893 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.003 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: UNAUTHENTICATED: Missing authorization header
14:47:51.003 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.114 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:51.114 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testExistsAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.395">
    <error message="org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries" type="java.util.concurrent.ExecutionException"><![CDATA[java.util.concurrent.ExecutionException: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.npci.rustyclient.client.NewMethodsTest.testExistsAsync(NewMethodsTest.java:143)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
Caused by: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.executeWithFailoverAsync(AsyncConnectionManager.java:97)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$5(AsyncConnectionManager.java:162)
	at java.base/java.util.concurrent.CompletableFuture$UniCompose.tryFire(CompletableFuture.java:1150)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.complete(CompletableFuture.java:2147)
	at org.npci.rustyclient.client.connection.AsyncConnectionManager.lambda$executeWithFailoverAsync$4(AsyncConnectionManager.java:161)
	at java.base/java.util.concurrent.ForkJoinTask$RunnableExecuteAction.exec(ForkJoinTask.java:1395)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
]]></error>
    <system-out><![CDATA[14:47:51.125 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.129 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.130 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:51.130 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:51.130 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:51.135 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.143 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.144 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:51.144 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:51.144 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:51.155 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:51.156 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.271 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:51.272 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.405 [ForkJoinPool.commonPool-worker-7] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:51.406 [ForkJoinPool.commonPool-worker-7] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
]]></system-out>
  </testcase>
  <testcase name="testHealthCheckAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.383">
    <system-out><![CDATA[14:47:51.524 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.528 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.528 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:51.528 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:51.528 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:51.533 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.538 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.538 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:51.538 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:51.538 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:51.560 [ForkJoinPool.commonPool-worker-2] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:51.562 [ForkJoinPool.commonPool-worker-2] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.682 [ForkJoinPool.commonPool-worker-7] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:51.683 [ForkJoinPool.commonPool-worker-7] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.794 [ForkJoinPool.commonPool-worker-7] WARN  o.n.r.c.c.AsyncConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNAUTHENTICATED: Missing authorization header
14:47:51.794 [ForkJoinPool.commonPool-worker-7] INFO  o.n.r.c.c.AsyncConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.901 [ForkJoinPool.commonPool-worker-7] WARN  o.n.r.client.RustyClusterAsyncClient - Ping failed: org.npci.rustyclient.client.exception.NoAvailableNodesException: Operation failed after 3 retries
]]></system-out>
  </testcase>
  <testcase name="testHealthCheck" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.285">
    <system-out><![CDATA[14:47:51.918 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.935 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.936 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:51.937 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:51.937 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:51.946 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:51.951 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:51.952 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:51.953 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:51.953 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:51.960 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:51.961 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:52.079 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50052, role=SECONDARY]: UNAUTHENTICATED: Missing authorization header
14:47:52.079 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:52.189 [main] WARN  o.n.r.c.connection.ConnectionManager - Operation failed on node NodeConfig[host=localhost, port=50051, role=PRIMARY]: UNAUTHENTICATED: Missing authorization header
14:47:52.191 [main] INFO  o.n.r.c.connection.ConnectionManager - Switched to node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:52.191 [main] WARN  o.n.r.client.RustyClusterClient - Ping failed: Operation failed after 3 retries
]]></system-out>
  </testcase>
  <testcase name="testLoadScriptAsync" classname="org.npci.rustyclient.client.NewMethodsTest" time="0.027">
    <system-out><![CDATA[14:47:52.199 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:52.206 [main] INFO  o.n.r.c.connection.ConnectionPool - Created connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:52.206 [main] INFO  o.n.r.c.connection.FailbackManager - FailbackManager started with check interval: 30000ms
14:47:52.207 [main] INFO  o.n.r.c.connection.ConnectionManager - ConnectionManager initialized with 2 nodes
14:47:52.207 [main] INFO  o.n.r.client.RustyClusterClient - RustyClusterClient initialized
14:47:52.212 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50051, role=PRIMARY]
14:47:52.217 [main] INFO  o.n.r.c.c.AsyncConnectionPool - Created async connection pool for node: NodeConfig[host=localhost, port=50052, role=SECONDARY]
14:47:52.218 [main] INFO  o.n.r.c.c.AsyncFailbackManager - AsyncFailbackManager started with check interval: 30000ms
14:47:52.218 [main] INFO  o.n.r.c.c.AsyncConnectionManager - AsyncConnectionManager initialized with 2 nodes
14:47:52.218 [main] INFO  o.n.r.client.RustyClusterAsyncClient - RustyClusterAsyncClient initialized
14:47:52.218 [main] WARN  o.n.r.client.RustyClusterAsyncClient - loadScriptAsync is not fully implemented yet - requires gRPC class regeneration
]]></system-out>
  </testcase>
</testsuite>