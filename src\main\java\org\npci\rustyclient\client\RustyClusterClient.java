package org.npci.rustyclient.client;

import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.connection.ConnectionManager;
import org.npci.rustyclient.client.connection.OperationType;
import org.npci.rustyclient.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Client for interacting with RustyCluster.
 */
public class RustyClusterClient implements AutoCloseable {
    private static final Logger logger = LoggerFactory.getLogger(RustyClusterClient.class);

    private final RustyClusterClientConfig config;
    private final ConnectionManager connectionManager;
    private final AuthenticationManager authenticationManager;

    /**
     * Create a new RustyClusterClient with the provided configuration.
     *
     * @param config The client configuration
     */
    public RustyClusterClient(RustyClusterClientConfig config) {
        this.config = config;
        this.authenticationManager = new AuthenticationManager(config);
        this.connectionManager = new ConnectionManager(config, authenticationManager);
        logger.info("RustyClusterClient initialized");
    }

    /**
     * Create a new RustyClusterClient with a custom connection manager (for testing).
     *
     * @param config The client configuration
     * @param connectionManager The connection manager to use
     */
    RustyClusterClient(RustyClusterClientConfig config, ConnectionManager connectionManager) {
        this.config = config;
        this.authenticationManager = new AuthenticationManager(config);
        this.connectionManager = connectionManager;
        logger.info("RustyClusterClient initialized");
    }

    /**
     * Set a key-value pair.
     *
     * @param key             The key
     * @param value           The value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value, boolean skipReplication) {
        logger.debug("Setting key: {}", key);

        try {
            RustyClusterProto.SetRequest request = RustyClusterProto.SetRequest.newBuilder()
                    .setKey(key)
                    .setValue(value)
                    .setSkipReplication(skipReplication)
                    .build();

            RustyClusterProto.SetResponse response = connectionManager.executeWithFailover(stub ->
                    stub.set(request), OperationType.WRITE);

            return response.getSuccess();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Set a key-value pair with default replication.
     *
     * @param key   The key
     * @param value The value
     * @return True if successful, false otherwise
     */
    public boolean set(String key, String value) {
        return set(key, value, false);
    }

    /**
     * Get a value by key.
     *
     * @param key The key
     * @return The value, or null if not found
     */
    public String get(String key) {
        logger.debug("Getting key: {}", key);
        RustyClusterProto.GetRequest request = RustyClusterProto.GetRequest.newBuilder()
                .setKey(key)
                .build();

        RustyClusterProto.GetResponse response = connectionManager.executeWithFailover(stub ->
                stub.get(request), OperationType.READ);

        return response.getFound() ? response.getValue() : null;
    }

    /**
     * Delete a key.
     *
     * @param key             The key
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean delete(String key, boolean skipReplication) {
        logger.debug("Deleting key: {}", key);
        RustyClusterProto.DeleteRequest request = RustyClusterProto.DeleteRequest.newBuilder()
                .setKey(key)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.DeleteResponse response = connectionManager.executeWithFailover(stub ->
                stub.delete(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Delete a key with default replication.
     *
     * @param key The key
     * @return True if successful, false otherwise
     */
    public boolean delete(String key) {
        return delete(key, false);
    }

    /**
     * Set a key-value pair with expiration.
     *
     * @param key             The key
     * @param value           The value
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl, boolean skipReplication) {
        logger.debug("Setting key with expiry: {}, ttl: {}", key, ttl);
        RustyClusterProto.SetExRequest request = RustyClusterProto.SetExRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setTtl(ttl)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.SetExResponse response = connectionManager.executeWithFailover(stub ->
                stub.setEx(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Set a key-value pair with expiration and default replication.
     *
     * @param key   The key
     * @param value The value
     * @param ttl   The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setEx(String key, String value, long ttl) {
        return setEx(key, value, ttl, false);
    }

    /**
     * Set expiration on an existing key.
     *
     * @param key             The key
     * @param ttl             The time-to-live in seconds
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl, boolean skipReplication) {
        logger.debug("Setting expiry on key: {}, ttl: {}", key, ttl);
        RustyClusterProto.SetExpiryRequest request = RustyClusterProto.SetExpiryRequest.newBuilder()
                .setKey(key)
                .setTtl(ttl)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.SetExpiryResponse response = connectionManager.executeWithFailover(stub ->
                stub.setExpiry(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Set expiration on an existing key with default replication.
     *
     * @param key The key
     * @param ttl The time-to-live in seconds
     * @return True if successful, false otherwise
     */
    public boolean setExpiry(String key, long ttl) {
        return setExpiry(key, ttl, false);
    }

    /**
     * Increment a numeric value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long incrBy(String key, long value, boolean skipReplication) {
        logger.debug("Incrementing key: {} by {}", key, value);
        RustyClusterProto.IncrByRequest request = RustyClusterProto.IncrByRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.IncrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.incrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a numeric value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public long incrBy(String key, long value) {
        return incrBy(key, value, false);
    }

    /**
     * Decrement a numeric value.
     *
     * @param key             The key
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long decrBy(String key, long value, boolean skipReplication) {
        logger.debug("Decrementing key: {} by {}", key, value);
        RustyClusterProto.DecrByRequest request = RustyClusterProto.DecrByRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.DecrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.decrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Decrement a numeric value with default replication.
     *
     * @param key   The key
     * @param value The decrement value
     * @return The new value
     */
    public long decrBy(String key, long value) {
        return decrBy(key, value, false);
    }

    /**
     * Increment a floating-point value.
     *
     * @param key             The key
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double incrByFloat(String key, double value, boolean skipReplication) {
        logger.debug("Incrementing key: {} by float {}", key, value);
        RustyClusterProto.IncrByFloatRequest request = RustyClusterProto.IncrByFloatRequest.newBuilder()
                .setKey(key)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.IncrByFloatResponse response = connectionManager.executeWithFailover(stub ->
                stub.incrByFloat(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a floating-point value with default replication.
     *
     * @param key   The key
     * @param value The increment value
     * @return The new value
     */
    public double incrByFloat(String key, double value) {
        return incrByFloat(key, value, false);
    }

    /**
     * Set a field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The field value
     * @param skipReplication Whether to skip replication
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value, boolean skipReplication) {
        logger.debug("Setting hash field: {}.{}", key, field);
        RustyClusterProto.HSetRequest request = RustyClusterProto.HSetRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.HSetResponse response = connectionManager.executeWithFailover(stub ->
                stub.hSet(request), OperationType.WRITE);

        return response.getSuccess();
    }

    /**
     * Set a field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The field value
     * @return True if successful, false otherwise
     */
    public boolean hSet(String key, String field, String value) {
        return hSet(key, field, value, false);
    }

    /**
     * Get a field from a hash.
     *
     * @param key   The hash key
     * @param field The field name
     * @return The field value, or null if not found
     */
    public String hGet(String key, String field) {
        logger.debug("Getting hash field: {}.{}", key, field);
        RustyClusterProto.HGetRequest request = RustyClusterProto.HGetRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .build();

        RustyClusterProto.HGetResponse response = connectionManager.executeWithFailover(stub ->
                stub.hGet(request), OperationType.READ);

        return response.getFound() ? response.getValue() : null;
    }

    /**
     * Get all fields from a hash.
     *
     * @param key The hash key
     * @return A map of field names to values
     */
    public Map<String, String> hGetAll(String key) {
        logger.debug("Getting all hash fields for key: {}", key);
        RustyClusterProto.HGetAllRequest request = RustyClusterProto.HGetAllRequest.newBuilder()
                .setKey(key)
                .build();

        RustyClusterProto.HGetAllResponse response = connectionManager.executeWithFailover(stub ->
                stub.hGetAll(request), OperationType.READ);

        return response.getFieldsMap();
    }

    /**
     * Increment a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value, boolean skipReplication) {
        logger.debug("Incrementing hash field: {}.{} by {}", key, field, value);
        RustyClusterProto.HIncrByRequest request = RustyClusterProto.HIncrByRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.HIncrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.hIncrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public long hIncrBy(String key, String field, long value) {
        return hIncrBy(key, field, value, false);
    }

    /**
     * Decrement a numeric field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The decrement value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value, boolean skipReplication) {
        logger.debug("Decrementing hash field: {}.{} by {}", key, field, value);
        RustyClusterProto.HDecrByRequest request = RustyClusterProto.HDecrByRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.HDecrByResponse response = connectionManager.executeWithFailover(stub ->
                stub.hDecrBy(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Decrement a numeric field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The decrement value
     * @return The new value
     */
    public long hDecrBy(String key, String field, long value) {
        return hDecrBy(key, field, value, false);
    }

    /**
     * Increment a floating-point field in a hash.
     *
     * @param key             The hash key
     * @param field           The field name
     * @param value           The increment value
     * @param skipReplication Whether to skip replication
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value, boolean skipReplication) {
        logger.debug("Incrementing hash field: {}.{} by float {}", key, field, value);
        RustyClusterProto.HIncrByFloatRequest request = RustyClusterProto.HIncrByFloatRequest.newBuilder()
                .setKey(key)
                .setField(field)
                .setValue(value)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.HIncrByFloatResponse response = connectionManager.executeWithFailover(stub ->
                stub.hIncrByFloat(request), OperationType.WRITE);

        return response.getNewValue();
    }

    /**
     * Increment a floating-point field in a hash with default replication.
     *
     * @param key   The hash key
     * @param field The field name
     * @param value The increment value
     * @return The new value
     */
    public double hIncrByFloat(String key, String field, double value) {
        return hIncrByFloat(key, field, value, false);
    }

    /**
     * Execute a batch of operations.
     *
     * @param operations      The list of operations to execute
     * @param skipReplication Whether to skip replication
     * @return A list of results, one for each operation
     */
    public List<Boolean> batchWrite(List<RustyClusterProto.BatchOperation> operations, boolean skipReplication) {
        logger.debug("Executing batch write with {} operations", operations.size());
        RustyClusterProto.BatchWriteRequest request = RustyClusterProto.BatchWriteRequest.newBuilder()
                .addAllOperations(operations)
                .setSkipReplication(skipReplication)
                .build();

        RustyClusterProto.BatchWriteResponse response = connectionManager.executeWithFailover(stub ->
                stub.batchWrite(request), OperationType.WRITE);

        return response.getOperationResultsList();
    }

    /**
     * Execute a batch of operations with default replication.
     *
     * @param operations The list of operations to execute
     * @return A list of results, one for each operation
     */
    public List<Boolean> batchWrite(List<RustyClusterProto.BatchOperation> operations) {
        return batchWrite(operations, false);
    }

    /**
     * Authenticate with the RustyCluster server.
     * This method should be called before performing any operations if authentication is configured.
     *
     * @return True if authentication was successful, false otherwise
     */
    public boolean authenticate() {
        logger.debug("Attempting to authenticate with RustyCluster server");

        if (!config.hasAuthentication()) {
            logger.debug("No authentication credentials configured");
            return true;
        }

        try {
            // Get a stub from the connection manager and authenticate
            return connectionManager.executeWithFailover(stub ->
                authenticationManager.authenticate(stub), OperationType.AUTH);
        } catch (Exception e) {
            logger.error("Authentication failed", e);
            return false;
        }
    }

    /**
     * Check if the client is currently authenticated.
     *
     * @return True if authenticated, false otherwise
     */
    public boolean isAuthenticated() {
        return authenticationManager.isAuthenticated();
    }

    /**
     * Get the current session token.
     *
     * @return The session token, or null if not authenticated
     */
    public String getSessionToken() {
        return authenticationManager.getSessionToken();
    }

    /**
     * Close the client and release all resources.
     */
    @Override
    public void close() {
        authenticationManager.clearAuthentication();
        connectionManager.close();
        logger.info("RustyClusterClient closed");
    }
}
