<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FailbackManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">com.rustycluster.client.connection</a> &gt; <span class="el_source">FailbackManager.java</span></div><h1>FailbackManager.java</h1><pre class="source lang-java linenums">package com.rustycluster.client.connection;

import com.rustycluster.client.config.NodeConfig;
import com.rustycluster.client.config.RustyClusterClientConfig;
import com.rustycluster.grpc.KeyValueServiceGrpc;
import com.rustycluster.grpc.RustyClusterProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Manages automatic failback to higher-priority nodes when they become available again.
 */
public class FailbackManager implements AutoCloseable {
<span class="fc" id="L20">    private static final Logger logger = LoggerFactory.getLogger(FailbackManager.class);</span>

    private final RustyClusterClientConfig config;
    private final ConnectionPool connectionPool;
    private final List&lt;NodeConfig&gt; sortedNodes;
    private final AtomicReference&lt;NodeConfig&gt; currentNode;
    private final ScheduledExecutorService scheduler;
<span class="fc" id="L27">    private volatile boolean running = false;</span>

    /**
     * Create a new FailbackManager.
     *
     * @param config         The client configuration
     * @param connectionPool The connection pool
     * @param sortedNodes    The list of nodes sorted by priority
     * @param currentNode    The current active node reference
     */
    public FailbackManager(RustyClusterClientConfig config, 
                          ConnectionPool connectionPool,
                          List&lt;NodeConfig&gt; sortedNodes,
<span class="fc" id="L40">                          AtomicReference&lt;NodeConfig&gt; currentNode) {</span>
<span class="fc" id="L41">        this.config = config;</span>
<span class="fc" id="L42">        this.connectionPool = connectionPool;</span>
<span class="fc" id="L43">        this.sortedNodes = sortedNodes;</span>
<span class="fc" id="L44">        this.currentNode = currentNode;</span>
<span class="fc" id="L45">        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -&gt; {</span>
<span class="fc" id="L46">            Thread t = new Thread(r, &quot;FailbackManager&quot;);</span>
<span class="fc" id="L47">            t.setDaemon(true);</span>
<span class="fc" id="L48">            return t;</span>
        });
<span class="fc" id="L50">    }</span>

    /**
     * Start the failback manager.
     */
    public void start() {
<span class="fc bfc" id="L56" title="All 2 branches covered.">        if (!config.isEnableFailback()) {</span>
<span class="fc" id="L57">            logger.debug(&quot;Failback is disabled, not starting FailbackManager&quot;);</span>
<span class="fc" id="L58">            return;</span>
        }

<span class="pc bpc" id="L61" title="1 of 2 branches missed.">        if (running) {</span>
<span class="nc" id="L62">            logger.warn(&quot;FailbackManager is already running&quot;);</span>
<span class="nc" id="L63">            return;</span>
        }

<span class="fc" id="L66">        running = true;</span>
<span class="fc" id="L67">        scheduler.scheduleWithFixedDelay(</span>
            this::checkForFailback,
<span class="fc" id="L69">            config.getFailbackCheckIntervalMs(),</span>
<span class="fc" id="L70">            config.getFailbackCheckIntervalMs(),</span>
            TimeUnit.MILLISECONDS
        );
        
<span class="fc" id="L74">        logger.info(&quot;FailbackManager started with check interval: {}ms&quot;, config.getFailbackCheckIntervalMs());</span>
<span class="fc" id="L75">    }</span>

    /**
     * Stop the failback manager.
     */
    public void stop() {
<span class="fc" id="L81">        running = false;</span>
<span class="fc" id="L82">        scheduler.shutdown();</span>
        try {
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {</span>
<span class="nc" id="L85">                scheduler.shutdownNow();</span>
            }
<span class="nc" id="L87">        } catch (InterruptedException e) {</span>
<span class="nc" id="L88">            scheduler.shutdownNow();</span>
<span class="nc" id="L89">            Thread.currentThread().interrupt();</span>
<span class="fc" id="L90">        }</span>
<span class="fc" id="L91">        logger.info(&quot;FailbackManager stopped&quot;);</span>
<span class="fc" id="L92">    }</span>

    /**
     * Check if we can failback to a higher-priority node.
     */
    private void checkForFailback() {
<span class="pc bpc" id="L98" title="1 of 2 branches missed.">        if (!running) {</span>
<span class="nc" id="L99">            return;</span>
        }

        try {
<span class="fc" id="L103">            NodeConfig current = currentNode.get();</span>
<span class="pc bpc" id="L104" title="1 of 2 branches missed.">            if (current == null) {</span>
<span class="nc" id="L105">                return;</span>
            }

            // Find the highest priority node that's available
<span class="fc" id="L109">            NodeConfig bestAvailableNode = findBestAvailableNode();</span>
            
<span class="fc bfc" id="L111" title="All 2 branches covered.">            if (bestAvailableNode != null &amp;&amp; </span>
<span class="pc bpc" id="L112" title="1 of 2 branches missed.">                bestAvailableNode.role().getPriority() &lt; current.role().getPriority()) {</span>
                
<span class="fc" id="L114">                logger.info(&quot;Failing back from {} (priority {}) to {} (priority {})&quot;,</span>
<span class="fc" id="L115">                    current, current.role().getPriority(),</span>
<span class="fc" id="L116">                    bestAvailableNode, bestAvailableNode.role().getPriority());</span>

<span class="fc" id="L118">                currentNode.set(bestAvailableNode);</span>

                // Clear authentication state when switching nodes during failback
                // This will force re-authentication on the next operation
<span class="fc bfc" id="L122" title="All 2 branches covered.">                if (config.hasAuthentication()) {</span>
<span class="fc" id="L123">                    connectionPool.getAuthenticationManager().clearAuthentication();</span>
<span class="fc" id="L124">                    logger.debug(&quot;Cleared authentication state for failback to: {}&quot;, bestAvailableNode);</span>
                }
            }
<span class="nc" id="L127">        } catch (Exception e) {</span>
<span class="nc" id="L128">            logger.warn(&quot;Error during failback check: {}&quot;, e.getMessage());</span>
<span class="fc" id="L129">        }</span>
<span class="fc" id="L130">    }</span>

    /**
     * Find the best available node (highest priority that's healthy).
     * Only checks nodes with higher priority than the current node.
     *
     * @return The best available node, or null if none are available
     */
    private NodeConfig findBestAvailableNode() {
<span class="fc" id="L139">        NodeConfig current = currentNode.get();</span>
<span class="pc bpc" id="L140" title="1 of 2 branches missed.">        if (current == null) {</span>
<span class="nc" id="L141">            return null;</span>
        }

        // Only check nodes with higher priority (lower priority number) than current
<span class="pc bpc" id="L145" title="1 of 2 branches missed.">        for (NodeConfig node : sortedNodes) {</span>
<span class="fc bfc" id="L146" title="All 2 branches covered.">            if (node.role().getPriority() &lt; current.role().getPriority()) {</span>
<span class="fc bfc" id="L147" title="All 2 branches covered.">                if (isNodeHealthy(node)) {</span>
<span class="fc" id="L148">                    return node;</span>
                }
            } else {
                // Since nodes are sorted by priority, we can break early
                // when we reach nodes with same or lower priority
                break;
            }
<span class="fc" id="L155">        }</span>
<span class="fc" id="L156">        return null;</span>
    }

    /**
     * Check if a node is healthy by performing multiple health checks.
     *
     * @param node The node to check
     * @return True if the node is healthy, false otherwise
     */
    private boolean isNodeHealthy(NodeConfig node) {
<span class="fc" id="L166">        int successfulChecks = 0;</span>
<span class="fc" id="L167">        int requiredChecks = config.getFailbackHealthCheckRetries();</span>

<span class="fc bfc" id="L169" title="All 2 branches covered.">        for (int i = 0; i &lt; requiredChecks; i++) {</span>
<span class="fc bfc" id="L170" title="All 2 branches covered.">            if (performHealthCheck(node)) {</span>
<span class="fc" id="L171">                successfulChecks++;</span>
            } else {
                // If any check fails, consider the node unhealthy
                break;
            }
            
            // Small delay between checks to avoid overwhelming the node
<span class="fc bfc" id="L178" title="All 2 branches covered.">            if (i &lt; requiredChecks - 1) {</span>
                try {
<span class="fc" id="L180">                    Thread.sleep(100);</span>
<span class="nc" id="L181">                } catch (InterruptedException e) {</span>
<span class="nc" id="L182">                    Thread.currentThread().interrupt();</span>
<span class="nc" id="L183">                    return false;</span>
<span class="fc" id="L184">                }</span>
            }
        }

<span class="fc bfc" id="L188" title="All 2 branches covered.">        boolean isHealthy = successfulChecks == requiredChecks;</span>
<span class="fc bfc" id="L189" title="All 2 branches covered.">        if (isHealthy) {</span>
<span class="fc" id="L190">            logger.debug(&quot;Node {} passed {}/{} health checks&quot;, node, successfulChecks, requiredChecks);</span>
        } else {
<span class="fc" id="L192">            logger.debug(&quot;Node {} failed health check ({}/{} successful)&quot;, node, successfulChecks, requiredChecks);</span>
        }
        
<span class="fc" id="L195">        return isHealthy;</span>
    }

    /**
     * Perform a single health check on a node.
     *
     * @param node The node to check
     * @return True if the health check passed, false otherwise
     */
    private boolean performHealthCheck(NodeConfig node) {
<span class="fc" id="L205">        KeyValueServiceGrpc.KeyValueServiceBlockingStub stub = null;</span>
        try {
<span class="fc" id="L207">            stub = connectionPool.borrowStub(node);</span>
            
            // Apply a short timeout for health checks
<span class="fc" id="L210">            KeyValueServiceGrpc.KeyValueServiceBlockingStub stubWithDeadline = </span>
<span class="fc" id="L211">                stub.withDeadlineAfter(1000, TimeUnit.MILLISECONDS);</span>
            
            // Perform a simple ping operation (get a non-existent key)
<span class="fc" id="L214">            RustyClusterProto.GetRequest healthCheckRequest = RustyClusterProto.GetRequest.newBuilder()</span>
<span class="fc" id="L215">                .setKey(&quot;__health_check__&quot;)</span>
<span class="fc" id="L216">                .build();</span>
            
<span class="fc" id="L218">            stubWithDeadline.get(healthCheckRequest);</span>
<span class="fc" id="L219">            return true;</span>
            
<span class="fc" id="L221">        } catch (Exception e) {</span>
<span class="fc" id="L222">            logger.debug(&quot;Health check failed for node {}: {}&quot;, node, e.getMessage());</span>
<span class="fc" id="L223">            return false;</span>
        } finally {
<span class="fc bfc" id="L225" title="All 2 branches covered.">            if (stub != null) {</span>
<span class="fc" id="L226">                connectionPool.returnStub(node, stub);</span>
            }
        }
    }

    @Override
    public void close() {
<span class="fc" id="L233">        stop();</span>
<span class="fc" id="L234">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>