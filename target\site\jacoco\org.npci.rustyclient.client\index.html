<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.client</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.client</span></div><h1>org.npci.rustyclient.client</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,323 of 2,106</td><td class="ctr2">37%</td><td class="bar">38 of 42</td><td class="ctr2">9%</td><td class="ctr1">129</td><td class="ctr2">194</td><td class="ctr1">322</td><td class="ctr2">558</td><td class="ctr1">110</td><td class="ctr2">173</td><td class="ctr1">0</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a2"><a href="RustyClusterClient.html" class="el_class">RustyClusterClient</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="95" height="10" title="1,069" alt="1,069"/><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="269" alt="269"/></td><td class="ctr2" id="c2">20%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="111" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">7%</td><td class="ctr1" id="f0">97</td><td class="ctr2" id="g0">120</td><td class="ctr1" id="h0">257</td><td class="ctr2" id="i0">333</td><td class="ctr1" id="j0">84</td><td class="ctr2" id="k0">106</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="RustyClusterAsyncClient.html" class="el_class">RustyClusterAsyncClient</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="254" alt="254"/><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="270" alt="270"/></td><td class="ctr2" id="c1">51%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">32</td><td class="ctr2" id="g1">55</td><td class="ctr1" id="h1">65</td><td class="ctr2" id="i1">129</td><td class="ctr1" id="j1">26</td><td class="ctr2" id="k1">49</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="BatchOperationBuilder.html" class="el_class">BatchOperationBuilder</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="244" alt="244"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">19</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">96</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">18</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>