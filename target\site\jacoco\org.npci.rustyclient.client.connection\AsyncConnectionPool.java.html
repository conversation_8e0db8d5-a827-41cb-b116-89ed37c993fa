<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AsyncConnectionPool.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">AsyncConnectionPool.java</span></div><h1>AsyncConnectionPool.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannel;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.npci.rustyclient.client.auth.AuthenticationManager;
import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;
import org.npci.rustyclient.client.interceptor.AuthenticationInterceptor;
import org.npci.rustyclient.grpc.KeyValueServiceGrpc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Asynchronous connection pool for RustyCluster gRPC clients.
 */
public class AsyncConnectionPool implements AutoCloseable {
<span class="fc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(AsyncConnectionPool.class);</span>

    private final RustyClusterClientConfig config;
    private final GrpcChannelFactory channelFactory;
    private final AuthenticationManager authenticationManager;
    private final Map&lt;String, GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt;&gt; stubPools;

    /**
     * Create a new AsyncConnectionPool.
     *
     * @param config The client configuration
     * @param authenticationManager The authentication manager
     */
<span class="nc" id="L40">    public AsyncConnectionPool(RustyClusterClientConfig config, AuthenticationManager authenticationManager) {</span>
<span class="nc" id="L41">        this.config = config;</span>
<span class="nc" id="L42">        this.channelFactory = new GrpcChannelFactory(config);</span>
<span class="nc" id="L43">        this.authenticationManager = authenticationManager;</span>
<span class="nc" id="L44">        this.stubPools = new HashMap&lt;&gt;();</span>

        // Initialize connection pools for each node
<span class="nc bnc" id="L47" title="All 2 branches missed.">        for (NodeConfig nodeConfig : config.getNodes()) {</span>
<span class="nc" id="L48">            GenericObjectPoolConfig&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; poolConfig = new GenericObjectPoolConfig&lt;&gt;();</span>
            
            // High-throughput optimizations for async operations
<span class="nc" id="L51">            poolConfig.setMaxTotal(config.getMaxConnectionsPerNode());</span>
<span class="nc" id="L52">            poolConfig.setMaxIdle(config.getMaxConnectionsPerNode());</span>
<span class="nc" id="L53">            poolConfig.setMinIdle(Math.max(1, config.getMaxConnectionsPerNode() / 4)); // Keep 25% warm</span>
            
            // Async-optimized validation
<span class="nc" id="L56">            poolConfig.setTestOnBorrow(false); // Disable for performance</span>
<span class="nc" id="L57">            poolConfig.setTestOnReturn(false); // Disable expensive validation</span>
<span class="nc" id="L58">            poolConfig.setTestWhileIdle(true); // Only validate idle connections</span>
<span class="nc" id="L59">            poolConfig.setTimeBetweenEvictionRuns(java.time.Duration.ofSeconds(30)); // More frequent cleanup</span>
            
            // High-throughput settings for async
<span class="nc" id="L62">            poolConfig.setBlockWhenExhausted(false); // Never block - fail fast</span>
<span class="nc" id="L63">            poolConfig.setMaxWait(java.time.Duration.ofMillis(50)); // Very quick timeout for async</span>
<span class="nc" id="L64">            poolConfig.setMinEvictableIdleTime(java.time.Duration.ofMinutes(2)); // Keep connections longer</span>
<span class="nc" id="L65">            poolConfig.setNumTestsPerEvictionRun(3); // Limit eviction overhead</span>
            
            // JMX monitoring for production
<span class="nc" id="L68">            poolConfig.setJmxEnabled(true);</span>
<span class="nc" id="L69">            poolConfig.setJmxNamePrefix(&quot;RustyClusterAsync-&quot; + nodeConfig.getAddress());</span>

<span class="nc" id="L71">            GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; pool =</span>
                new GenericObjectPool&lt;&gt;(new AsyncStubFactory(nodeConfig), poolConfig);

<span class="nc" id="L74">            stubPools.put(nodeConfig.getAddress(), pool);</span>
<span class="nc" id="L75">            logger.info(&quot;Created async connection pool for node: {}&quot;, nodeConfig);</span>
<span class="nc" id="L76">        }</span>
<span class="nc" id="L77">    }</span>

    /**
     * Borrow a client stub from the pool for the specified node asynchronously.
     *
     * @param nodeConfig The node configuration
     * @return CompletableFuture that completes with a client stub
     */
    public CompletableFuture&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; borrowStubAsync(NodeConfig nodeConfig) {
<span class="nc" id="L86">        GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; pool = stubPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L88">            return CompletableFuture.failedFuture(</span>
                new IllegalArgumentException(&quot;No pool found for node: &quot; + nodeConfig));
        }
        
<span class="nc" id="L92">        return CompletableFuture.supplyAsync(() -&gt; {</span>
            try {
<span class="nc" id="L94">                return pool.borrowObject();</span>
<span class="nc" id="L95">            } catch (Exception e) {</span>
<span class="nc" id="L96">                throw new RuntimeException(&quot;Failed to borrow stub from pool&quot;, e);</span>
            }
        });
    }

    /**
     * Return a client stub to the pool.
     *
     * @param nodeConfig The node configuration
     * @param stub       The client stub to return
     */
    public void returnStub(NodeConfig nodeConfig, KeyValueServiceGrpc.KeyValueServiceFutureStub stub) {
<span class="nc" id="L108">        GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; pool = stubPools.get(nodeConfig.getAddress());</span>
<span class="nc bnc" id="L109" title="All 2 branches missed.">        if (pool == null) {</span>
<span class="nc" id="L110">            logger.warn(&quot;No pool found for node: {}&quot;, nodeConfig);</span>
<span class="nc" id="L111">            return;</span>
        }
<span class="nc" id="L113">        pool.returnObject(stub);</span>
<span class="nc" id="L114">    }</span>

    /**
     * Get the authentication manager.
     *
     * @return The authentication manager
     */
    public AuthenticationManager getAuthenticationManager() {
<span class="nc" id="L122">        return authenticationManager;</span>
    }

    /**
     * Close the connection pool and release all resources.
     */
    @Override
    public void close() {
<span class="nc bnc" id="L130" title="All 2 branches missed.">        for (GenericObjectPool&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; pool : stubPools.values()) {</span>
<span class="nc" id="L131">            pool.close();</span>
<span class="nc" id="L132">        }</span>
<span class="nc" id="L133">        stubPools.clear();</span>
<span class="nc" id="L134">        logger.info(&quot;Async connection pool closed&quot;);</span>
<span class="nc" id="L135">    }</span>

    /**
     * Factory for creating and validating async client stubs.
     */
    private class AsyncStubFactory extends BasePooledObjectFactory&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; {
        private final NodeConfig nodeConfig;

<span class="nc" id="L143">        AsyncStubFactory(NodeConfig nodeConfig) {</span>
<span class="nc" id="L144">            this.nodeConfig = nodeConfig;</span>
<span class="nc" id="L145">        }</span>

        @Override
        public KeyValueServiceGrpc.KeyValueServiceFutureStub create() {
<span class="nc" id="L149">            ManagedChannel channel = channelFactory.createChannel(nodeConfig);</span>

            // Create channel with authentication interceptor
<span class="nc" id="L152">            var interceptedChannel = ClientInterceptors.intercept(channel,</span>
                    new AuthenticationInterceptor(authenticationManager));

            // Don't set deadline here - it will be set per-operation to avoid
            // &quot;ClientCall started after CallOptions deadline was exceeded&quot; errors
<span class="nc" id="L157">            return KeyValueServiceGrpc.newFutureStub(interceptedChannel);</span>
        }

        @Override
        public PooledObject&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; wrap(KeyValueServiceGrpc.KeyValueServiceFutureStub stub) {
<span class="nc" id="L162">            return new DefaultPooledObject&lt;&gt;(stub);</span>
        }

        @Override
        public boolean validateObject(PooledObject&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; p) {
            // For async operations, we'll skip validation for performance
            // In a real implementation, you might want to perform an async health check
<span class="nc" id="L169">            return true;</span>
        }

        @Override
        public void destroyObject(PooledObject&lt;KeyValueServiceGrpc.KeyValueServiceFutureStub&gt; p) {
<span class="nc" id="L174">            KeyValueServiceGrpc.KeyValueServiceFutureStub stub = p.getObject();</span>
<span class="nc" id="L175">            ManagedChannel channel = (ManagedChannel) stub.getChannel();</span>
            try {
<span class="nc" id="L177">                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);</span>
<span class="nc" id="L178">            } catch (InterruptedException e) {</span>
<span class="nc" id="L179">                logger.warn(&quot;Interrupted while shutting down channel&quot;, e);</span>
<span class="nc" id="L180">                Thread.currentThread().interrupt();</span>
<span class="nc" id="L181">            }</span>
<span class="nc" id="L182">        }</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>