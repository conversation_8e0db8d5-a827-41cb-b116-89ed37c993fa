<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>org.npci.rustyclient.client</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <span class="el_package">org.npci.rustyclient.client</span></div><h1>org.npci.rustyclient.client</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">702 of 1,608</td><td class="ctr2">56%</td><td class="bar">25 of 30</td><td class="ctr2">16%</td><td class="ctr1">69</td><td class="ctr2">144</td><td class="ctr1">172</td><td class="ctr2">440</td><td class="ctr1">56</td><td class="ctr2">129</td><td class="ctr1">0</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a2"><a href="RustyClusterClient.java.html" class="el_source">RustyClusterClient.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="64" height="10" title="448" alt="448"/><img src="../jacoco-resources/greenbar.gif" width="56" height="10" title="392" alt="392"/></td><td class="ctr2" id="c2">46%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">18%</td><td class="ctr1" id="f0">37</td><td class="ctr2" id="g0">70</td><td class="ctr1" id="h0">107</td><td class="ctr2" id="i0">215</td><td class="ctr1" id="j0">30</td><td class="ctr2" id="k0">62</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="RustyClusterAsyncClient.java.html" class="el_source">RustyClusterAsyncClient.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="254" alt="254"/><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="270" alt="270"/></td><td class="ctr2" id="c1">51%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">32</td><td class="ctr2" id="g1">55</td><td class="ctr1" id="h1">65</td><td class="ctr2" id="i1">129</td><td class="ctr1" id="j1">26</td><td class="ctr2" id="k1">49</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="BatchOperationBuilder.java.html" class="el_source">BatchOperationBuilder.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="244" alt="244"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">19</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">96</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">18</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>