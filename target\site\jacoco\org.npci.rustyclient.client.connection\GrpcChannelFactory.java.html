<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GrpcChannelFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">GrpcChannelFactory.java</span></div><h1>GrpcChannelFactory.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;

import java.io.File;
import java.util.concurrent.TimeUnit;

import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

/**
 * Factory for creating gRPC channels to RustyCluster nodes.
 */
public class GrpcChannelFactory {
    private final RustyClusterClientConfig config;

    /**
     * Create a new GrpcChannelFactory.
     *
     * @param config The client configuration
     */
<span class="nc" id="L26">    public GrpcChannelFactory(RustyClusterClientConfig config) {</span>
<span class="nc" id="L27">        this.config = config;</span>
<span class="nc" id="L28">    }</span>

    /**
     * Create a new gRPC channel for the given node.
     *
     * @param nodeConfig The node configuration
     * @return A new ManagedChannel instance
     */
    public ManagedChannel createChannel(NodeConfig nodeConfig) {
<span class="nc bnc" id="L37" title="All 2 branches missed.">        if (config.isUseSecureConnection()) {</span>
<span class="nc" id="L38">            return createSecureChannel(nodeConfig);</span>
        } else {
<span class="nc" id="L40">            return createInsecureChannel(nodeConfig);</span>
        }
    }

    private ManagedChannel createInsecureChannel(NodeConfig nodeConfig) {
<span class="nc" id="L45">        return ManagedChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())</span>
<span class="nc" id="L46">                .usePlaintext()</span>
                // High-throughput keep-alive settings
<span class="nc" id="L48">                .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive</span>
<span class="nc" id="L49">                .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection</span>
<span class="nc" id="L50">                .keepAliveWithoutCalls(true)</span>
                // Performance optimizations
<span class="nc" id="L52">                .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches</span>
<span class="nc" id="L53">                .maxInboundMetadataSize(8 * 1024) // 8KB metadata</span>
                // Connection management
<span class="nc" id="L55">                .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections</span>
                // Retry configuration
<span class="nc" id="L57">                .enableRetry()</span>
<span class="nc" id="L58">                .maxRetryAttempts(3)</span>
<span class="nc" id="L59">                .build();</span>
    }

    private ManagedChannel createSecureChannel(NodeConfig nodeConfig) {
        try {
<span class="nc" id="L64">            SslContext sslContext = GrpcSslContexts.forClient()</span>
<span class="nc" id="L65">                    .trustManager(new File(config.getTlsCertPath()))</span>
<span class="nc" id="L66">                    .build();</span>

<span class="nc" id="L68">            return NettyChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())</span>
<span class="nc" id="L69">                    .sslContext(sslContext)</span>
                    // High-throughput keep-alive settings
<span class="nc" id="L71">                    .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive</span>
<span class="nc" id="L72">                    .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection</span>
<span class="nc" id="L73">                    .keepAliveWithoutCalls(true)</span>
                    // Performance optimizations
<span class="nc" id="L75">                    .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches</span>
<span class="nc" id="L76">                    .maxInboundMetadataSize(8 * 1024) // 8KB metadata</span>
                    // Connection management
<span class="nc" id="L78">                    .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections</span>
                    // Retry configuration
<span class="nc" id="L80">                    .enableRetry()</span>
<span class="nc" id="L81">                    .maxRetryAttempts(3)</span>
<span class="nc" id="L82">                    .build();</span>
<span class="nc" id="L83">        } catch (Exception e) {</span>
<span class="nc" id="L84">            throw new RuntimeException(&quot;Failed to create secure channel&quot;, e);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>