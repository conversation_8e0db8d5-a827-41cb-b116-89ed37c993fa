<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GrpcChannelFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">RustyCluster Java Client</a> &gt; <a href="index.source.html" class="el_package">org.npci.rustyclient.client.connection</a> &gt; <span class="el_source">GrpcChannelFactory.java</span></div><h1>GrpcChannelFactory.java</h1><pre class="source lang-java linenums">package org.npci.rustyclient.client.connection;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;

import java.io.File;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.npci.rustyclient.client.config.NodeConfig;
import org.npci.rustyclient.client.config.RustyClusterClientConfig;

/**
 * Factory for creating and caching gRPC channels to RustyCluster nodes.
 * Implements channel reuse optimization for better performance and resource utilization.
 */
public class GrpcChannelFactory {
    private final RustyClusterClientConfig config;
<span class="nc" id="L22">    private final ConcurrentHashMap&lt;String, ManagedChannel&gt; channelCache = new ConcurrentHashMap&lt;&gt;();</span>

    /**
     * Create a new GrpcChannelFactory.
     *
     * @param config The client configuration
     */
<span class="nc" id="L29">    public GrpcChannelFactory(RustyClusterClientConfig config) {</span>
<span class="nc" id="L30">        this.config = config;</span>
<span class="nc" id="L31">    }</span>

    /**
     * Get or create a gRPC channel for the given node.
     * Implements channel reuse optimization for better performance.
     *
     * @param nodeConfig The node configuration
     * @return A cached or new ManagedChannel instance
     */
    public ManagedChannel createChannel(NodeConfig nodeConfig) {
<span class="nc" id="L41">        return channelCache.computeIfAbsent(nodeConfig.getAddress(),</span>
            address -&gt; {
<span class="nc bnc" id="L43" title="All 2 branches missed.">                if (config.isUseSecureConnection()) {</span>
<span class="nc" id="L44">                    return createSecureChannel(nodeConfig);</span>
                } else {
<span class="nc" id="L46">                    return createInsecureChannel(nodeConfig);</span>
                }
            });
    }

    /**
     * Close all cached channels and clear the cache.
     * Should be called when the factory is no longer needed.
     */
    public void close() {
<span class="nc" id="L56">        channelCache.values().forEach(channel -&gt; {</span>
            try {
<span class="nc" id="L58">                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);</span>
<span class="nc" id="L59">            } catch (InterruptedException e) {</span>
<span class="nc" id="L60">                Thread.currentThread().interrupt();</span>
<span class="nc" id="L61">            }</span>
<span class="nc" id="L62">        });</span>
<span class="nc" id="L63">        channelCache.clear();</span>
<span class="nc" id="L64">    }</span>

    private ManagedChannel createInsecureChannel(NodeConfig nodeConfig) {
<span class="nc" id="L67">        return ManagedChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())</span>
<span class="nc" id="L68">                .usePlaintext()</span>
                // High-throughput keep-alive settings
<span class="nc" id="L70">                .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive</span>
<span class="nc" id="L71">                .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection</span>
<span class="nc" id="L72">                .keepAliveWithoutCalls(true)</span>
                // Performance optimizations
<span class="nc" id="L74">                .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches</span>
<span class="nc" id="L75">                .maxInboundMetadataSize(8 * 1024) // 8KB metadata</span>
                // Connection management
<span class="nc" id="L77">                .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections</span>
                // Retry configuration
<span class="nc" id="L79">                .enableRetry()</span>
<span class="nc" id="L80">                .maxRetryAttempts(3)</span>
<span class="nc" id="L81">                .build();</span>
    }

    private ManagedChannel createSecureChannel(NodeConfig nodeConfig) {
        try {
<span class="nc" id="L86">            SslContext sslContext = GrpcSslContexts.forClient()</span>
<span class="nc" id="L87">                    .trustManager(new File(config.getTlsCertPath()))</span>
<span class="nc" id="L88">                    .build();</span>

<span class="nc" id="L90">            return NettyChannelBuilder.forAddress(nodeConfig.host(), nodeConfig.port())</span>
<span class="nc" id="L91">                    .sslContext(sslContext)</span>
                    // High-throughput keep-alive settings
<span class="nc" id="L93">                    .keepAliveTime(15, TimeUnit.SECONDS) // More aggressive keep-alive</span>
<span class="nc" id="L94">                    .keepAliveTimeout(5, TimeUnit.SECONDS) // Faster timeout detection</span>
<span class="nc" id="L95">                    .keepAliveWithoutCalls(true)</span>
                    // Performance optimizations
<span class="nc" id="L97">                    .maxInboundMessageSize(50 * 1024 * 1024) // 50MB for large batches</span>
<span class="nc" id="L98">                    .maxInboundMetadataSize(8 * 1024) // 8KB metadata</span>
                    // Connection management
<span class="nc" id="L100">                    .idleTimeout(5, TimeUnit.MINUTES) // Close idle connections</span>
                    // Retry configuration
<span class="nc" id="L102">                    .enableRetry()</span>
<span class="nc" id="L103">                    .maxRetryAttempts(3)</span>
<span class="nc" id="L104">                    .build();</span>
<span class="nc" id="L105">        } catch (Exception e) {</span>
<span class="nc" id="L106">            throw new RuntimeException(&quot;Failed to create secure channel&quot;, e);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>