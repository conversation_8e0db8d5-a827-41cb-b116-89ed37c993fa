G<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUCTION_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON><PERSON>_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED
RustyCluster Java Client,org.npci.rustyclient.client.interceptor,AuthenticationInterceptor,24,0,0,0,8,0,3,0,3,0
RustyCluster Java Client,org.npci.rustyclient.client.interceptor,AuthenticationInterceptor.new ForwardingClientCall.SimpleForwardingClientCall() {...},38,0,4,0,8,0,4,0,2,0
RustyCluster Java Client,org.npci.rustyclient.client.metrics,PerformanceMetrics.PerformanceStats,48,0,0,0,1,0,1,0,1,0
RustyCluster Java Client,org.npci.rustyclient.client.metrics,PerformanceMetrics,422,0,8,0,87,0,25,0,21,0
RustyCluster Java Client,org.npci.rustyclient.client.connection,AsyncFailbackManager,103,337,11,17,25,85,14,23,3,20
RustyCluster Java Client,org.npci.rustyclient.client.connection,FailbackManager,49,326,8,32,19,84,8,22,0,10
RustyCluster Java Client,org.npci.rustyclient.client.connection,AsyncConnectionPool.AsyncStubFactory,63,0,0,0,16,0,5,0,5,0
RustyCluster Java Client,org.npci.rustyclient.client.connection,ConnectionPool,170,4,8,0,42,1,9,1,5,1
RustyCluster Java Client,org.npci.rustyclient.client.connection,AsyncFailbackManager.new FutureCallback() {...},6,15,0,0,2,3,1,2,1,2
RustyCluster Java Client,org.npci.rustyclient.client.connection,AsyncConnectionPool,182,4,8,0,45,1,10,1,6,1
RustyCluster Java Client,org.npci.rustyclient.client.connection,GrpcChannelFactory,91,0,2,0,33,0,5,0,4,0
RustyCluster Java Client,org.npci.rustyclient.client.connection,ConnectionPool.StubFactory,63,0,0,0,16,0,5,0,5,0
RustyCluster Java Client,org.npci.rustyclient.client.connection,OperationType,0,21,0,0,0,4,0,1,0,1
RustyCluster Java Client,org.npci.rustyclient.client.connection,AsyncConnectionManager,485,0,50,0,110,0,51,0,25,0
RustyCluster Java Client,org.npci.rustyclient.client.connection,ConnectionManager,52,378,15,45,12,93,15,29,1,12
RustyCluster Java Client,org.npci.rustyclient.client,RustyClusterClient,399,258,4,2,101,74,30,23,28,22
RustyCluster Java Client,org.npci.rustyclient.client,RustyClusterAsyncClient,248,0,4,0,74,0,26,0,24,0
RustyCluster Java Client,org.npci.rustyclient.client,BatchOperationBuilder,0,198,0,0,0,85,0,13,0,13
RustyCluster Java Client,org.npci.rustyclient.client.auth,AuthenticationManager,34,86,1,3,9,26,1,7,0,6
RustyCluster Java Client,org.npci.rustyclient.client.exception,NoAvailableNodesException,4,5,0,0,2,2,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.client.config,NodeRole,0,35,0,0,0,8,0,3,0,3
RustyCluster Java Client,org.npci.rustyclient.client.config,NodeConfig,0,18,0,0,0,2,0,2,0,2
RustyCluster Java Client,org.npci.rustyclient.client.config,RustyClusterClientConfig,0,104,1,3,0,31,1,18,0,17
RustyCluster Java Client,org.npci.rustyclient.client.config,RustyClusterClientConfig.Builder,16,350,3,15,4,86,3,31,0,25
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExpiryRequest.Builder,420,0,39,0,128,0,54,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchWriteResponse,317,70,29,3,84,21,45,7,29,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HDecrByRequest.Builder,556,0,52,0,168,0,66,0,38,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByResponse,286,0,22,0,79,0,44,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetResponse,221,60,18,4,60,19,36,8,25,8
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetAllResponse.FieldsDefaultEntryHolder,0,8,0,0,0,2,0,1,0,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByRequest,512,0,48,0,139,0,62,0,38,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByFloatRequest,428,0,38,0,116,0,55,0,36,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByRequest,341,82,34,4,91,24,46,9,27,9
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.AuthenticateResponse,372,87,37,5,101,26,49,9,28,9
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.PingResponse,423,0,38,0,115,0,55,0,36,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.AsyncService,68,0,0,0,34,0,17,0,17,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByFloatRequest.Builder,556,0,52,0,168,0,66,0,38,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetAllResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HSetRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DecrByRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByFloatResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto,4,811,0,0,3,78,2,2,2,2
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.GetRequest.Builder,244,56,25,4,75,19,37,5,22,5
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchWriteRequest,305,62,27,3,76,19,45,8,30,8
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DecrByResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchWriteResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.KeyValueServiceFileDescriptorSupplier,3,0,0,0,1,0,1,0,1,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetAllResponse.Builder,334,85,36,9,90,26,52,7,30,6
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HSetResponse,281,0,22,0,79,0,44,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetResponse.Builder,359,0,34,0,111,0,48,0,30,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExResponse.Builder,172,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByFloatRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByFloatResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.KeyValueServiceStub,181,0,0,0,54,0,19,0,19,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchOperation.OperationType.new Internal.EnumLiteMap() {...},3,3,0,0,1,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.MethodHandlers,143,0,18,0,43,0,20,0,3,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HSetResponse.Builder,220,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByFloatRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.AuthenticateResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExRequest,412,100,43,5,110,29,52,10,28,10
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetRequest,368,91,37,5,100,27,49,9,28,9
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.KeyValueServiceBlockingStub,164,0,0,0,37,0,19,0,19,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetAllRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.KeyValueServiceMethodDescriptorSupplier,12,0,0,0,4,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByResponse,231,55,19,3,62,17,37,7,26,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.PingResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HDecrByRequest,512,0,48,0,139,0,62,0,38,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DecrByRequest.Builder,420,0,39,0,128,0,54,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByFloatRequest,517,0,48,0,140,0,62,0,38,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetAllResponse,333,56,32,4,86,18,50,8,32,8
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.GetResponse.Builder,282,77,27,7,86,25,40,8,24,6
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DeleteRequest,297,73,28,4,81,22,43,8,27,8
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DeleteResponse.Builder,172,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExpiryResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.KeyValueServiceImplBase,6,0,0,0,2,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchOperation.OperationType,55,131,8,12,14,32,11,14,6,4
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.GetRequest,258,64,22,4,71,20,40,7,27,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DecrByRequest,423,0,38,0,115,0,55,0,36,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetResponse.Builder,172,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByRequest.Builder,556,0,52,0,168,0,66,0,38,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HDecrByResponse.Builder,222,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExRequest.Builder,432,124,44,8,129,39,58,8,30,8
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.AuthenticateRequest.Builder,354,82,36,6,107,27,48,6,26,6
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchOperation.Builder,681,222,69,18,194,67,86,16,44,11
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchWriteRequest.Builder,602,115,75,7,168,35,80,8,38,8
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DeleteResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByFloatResponse.Builder,222,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DeleteResponse,226,55,19,3,62,17,37,7,26,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByFloatRequest.Builder,420,0,39,0,128,0,54,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExpiryResponse,281,0,22,0,79,0,44,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.AuthenticateRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.AuthenticateResponse.Builder,392,103,39,8,118,33,52,8,28,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HSetRequest.Builder,631,0,60,0,191,0,72,0,40,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByFloatResponse.Builder,222,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DecrByResponse.Builder,222,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DeleteRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetAllRequest.Builder,244,56,25,4,75,19,37,5,22,5
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchOperation.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetRequest,411,0,36,0,115,0,54,0,36,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HDecrByRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HSetResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.AuthenticateRequest,347,64,33,3,96,19,48,6,30,6
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HDecrByResponse.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByResponse.Builder,174,48,18,3,54,15,31,4,20,4
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByFloatResponse,291,0,22,0,80,0,44,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.KeyValueServiceFutureStub,164,0,0,0,37,0,19,0,19,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchWriteRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HSetRequest,548,0,52,0,151,0,65,0,39,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.GetRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExpiryRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExpiryResponse.Builder,220,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.GetResponse,292,78,27,5,79,24,42,9,26,9
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DeleteRequest.Builder,282,77,29,5,86,25,42,6,24,6
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByFloatResponse,291,0,22,0,80,0,44,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.PingResponse.Builder,420,0,39,0,128,0,54,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,KeyValueServiceGrpc.KeyValueServiceBaseDescriptorSupplier,10,0,0,0,3,0,3,0,3,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetRequest.Builder,436,0,42,0,134,0,54,0,32,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByRequest.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExResponse,226,55,19,3,62,17,37,7,26,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetAllRequest,258,64,22,4,71,20,40,7,27,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HIncrByResponse.Builder,222,0,21,0,69,0,35,0,24,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.IncrByRequest.Builder,322,98,33,6,97,31,47,7,26,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetRequest.Builder,392,103,39,8,118,33,52,8,28,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.GetResponse.new AbstractParser() {...},33,3,0,0,11,1,1,1,1,1
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchWriteResponse.Builder,332,98,32,5,97,30,47,7,27,7
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.PingRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.PingRequest,233,0,16,0,67,0,40,0,32,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.PingRequest.Builder,148,0,14,0,48,0,27,0,20,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.BatchOperation,672,144,97,7,165,40,86,13,34,13
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.DecrByResponse,286,0,22,0,79,0,44,0,33,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HGetResponse,370,0,32,0,103,0,51,0,35,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.SetExpiryRequest,423,0,38,0,115,0,55,0,36,0
RustyCluster Java Client,org.npci.rustyclient.grpc,RustyClusterProto.HDecrByResponse,286,0,22,0,79,0,44,0,33,0
